#!/bin/bash

# 虚拟人恋爱陪伴系统 - Memobase版本启动脚本

echo "🚀 启动虚拟人恋爱陪伴系统 (Memobase版本)"
echo "=================================================="

# 检查Docker是否运行
if ! docker ps >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker Desktop"
    exit 1
fi

# 检查Memobase服务器状态
echo "🔍 检查Memobase服务器状态..."
if curl -s http://localhost:8019/ping >/dev/null 2>&1; then
    echo "✅ Memobase服务器已运行"
else
    echo "⚠️ Memobase服务器未运行，尝试启动..."
    
    # 进入Memobase目录并启动服务
    cd memobase/src/server
    
    # 检查docker-compose是否可用
    if command -v docker-compose >/dev/null 2>&1; then
        COMPOSE_CMD="docker-compose"
    elif [ -f "/usr/local/Cellar/docker-compose/2.36.2/bin/docker-compose" ]; then
        COMPOSE_CMD="/usr/local/Cellar/docker-compose/2.36.2/bin/docker-compose"
    else
        echo "❌ 找不到docker-compose命令"
        echo "💡 请手动启动Memobase服务器或使用降级模式"
        cd ../../..
        echo "🔄 使用SQLite降级模式启动..."
        export USE_MEMOBASE=false
        python start.py
        exit 0
    fi
    
    # 启动Memobase服务
    echo "🐳 启动Memobase Docker服务..."
    $COMPOSE_CMD up -d
    
    if [ $? -eq 0 ]; then
        echo "✅ Memobase服务启动成功"
        echo "⏳ 等待服务就绪..."
        sleep 10
        
        # 验证服务是否就绪
        for i in {1..30}; do
            if curl -s http://localhost:8019/ping >/dev/null 2>&1; then
                echo "✅ Memobase服务就绪"
                break
            fi
            echo "⏳ 等待服务就绪... ($i/30)"
            sleep 2
        done
        
        if ! curl -s http://localhost:8019/ping >/dev/null 2>&1; then
            echo "⚠️ Memobase服务启动超时，使用降级模式"
            cd ../../..
            export USE_MEMOBASE=false
            python start.py
            exit 0
        fi
    else
        echo "❌ Memobase服务启动失败，使用降级模式"
        cd ../../..
        export USE_MEMOBASE=false
        python start.py
        exit 0
    fi
    
    cd ../../..
fi

# 检查环境配置
echo "🔧 检查环境配置..."
if [ ! -f "backend/.env" ]; then
    echo "⚠️ 环境配置文件不存在，创建默认配置..."
    cp backend/.env.example backend/.env
    echo "💡 请编辑 backend/.env 文件，配置火山引擎API密钥"
fi

# 运行Memobase设置测试
echo "🧪 运行Memobase设置测试..."
python test_memobase_setup.py

if [ $? -eq 0 ]; then
    echo "✅ Memobase设置测试通过"
else
    echo "⚠️ Memobase设置测试失败，使用降级模式"
    export USE_MEMOBASE=false
fi

# 启动主程序
echo "🎯 启动虚拟人恋爱陪伴系统..."
python start.py
