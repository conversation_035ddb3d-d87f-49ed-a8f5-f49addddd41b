# 火山引擎配置
VOLCENGINE_ACCESS_KEY=your_access_key_here
VOLCENGINE_SECRET_KEY=your_secret_key_here
VOLCENGINE_REGION=cn-beijing
VOLCENGINE_MODEL=doubao-lite-4k

# 本地记忆系统配置
USE_LOCAL_MEMORY=true
MEMORY_DATABASE_URL=postgresql://postgres:password@localhost:5432/virtual_memory
MEMORY_PROJECT_ID=virtual_companion
MEMORY_MAX_TOKEN_SIZE=500
MEMORY_AUTO_FLUSH=true

# 消息分段配置
USE_LLM_SPLIT=false

# 项目实例配置（用于多实例部署）
PROJECT_INSTANCE_ID=default

# 原Memobase配置（保留用于兼容性）
MEMOBASE_PROJECT_URL=http://localhost:8019
MEMOBASE_API_KEY=secret
MEMOBASE_PROJECT_ID=memobase_dev
MEMOBASE_MAX_TOKEN_SIZE=500
MEMOBASE_AUTO_FLUSH=true
