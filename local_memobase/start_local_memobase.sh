#!/bin/bash

echo "🚀 启动本地Memobase服务"
echo "================================"

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 创建数据目录
echo "📁 创建数据目录..."
mkdir -p data/postgres
mkdir -p data/redis

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "❌ 未找到.env文件，请先创建配置文件"
    exit 1
fi

# 检查配置文件
if [ ! -f config.yaml ]; then
    echo "❌ 未找到config.yaml文件，请先创建配置文件"
    exit 1
fi

# 停止现有服务
echo "🛑 停止现有服务..."
docker compose down

# 启动服务
echo "🚀 启动Memobase服务..."
docker compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker compose ps

# 检查API健康状态
echo "🏥 检查API健康状态..."
for i in {1..30}; do
    if curl -s http://localhost:8019/healthcheck > /dev/null; then
        echo "✅ Memobase API服务已启动"
        echo "🌐 API地址: http://localhost:8019"
        echo "📊 数据库端口: 5433"
        echo "🔴 Redis端口: 6380"
        echo ""
        echo "🔧 配置项目使用本地Memobase:"
        echo "   MEMOBASE_PROJECT_URL=http://localhost:8019"
        echo "   MEMOBASE_API_KEY=local_memobase_token_123"
        exit 0
    fi
    echo "等待API启动... ($i/30)"
    sleep 2
done

echo "❌ API启动超时，请检查日志:"
echo "   docker compose logs memobase-server-api"
