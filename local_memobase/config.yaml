# Memobase本地配置 - 针对虚拟人恋爱陪伴系统优化

# LLM配置 - 使用火山引擎
llm_api_key: beccd81d-5d3f-4f76-82a2-57ce4a047f99
llm_base_url: https://ark.cn-beijing.volces.com/api/v3
best_llm_model: doubao-1-5-pro-256k-250115

# Embedding配置 - 使用OpenAI兼容模型（2560维度）
embedding_provider: openai
embedding_api_key: beccd81d-5d3f-4f76-82a2-57ce4a047f99
embedding_base_url: https://ark.cn-beijing.volces.com/api/v3
embedding_model: doubao-embedding-text-240715
embedding_dim: 2560

# Token优化配置
max_tokens_per_request: 2000
enable_token_optimization: true
cache_embeddings: true

# 缓冲区管理配置
buffer_config:
  max_buffer_size: 1024  # 最大缓冲区大小
  auto_flush_threshold: 800  # 自动刷新阈值
  batch_size: 5  # 批处理大小

# 虚拟人恋爱陪伴系统 - 用户画像配置
# 针对沈沐心（沐沐）虚拟人的用户记忆分类
overwrite_user_profiles:
  # 基础信息类
  - topic: "basic_info"
    description: "用户基础个人信息"
    sub_topics:
      - name: "personal_details"
        description: "姓名、年龄、性别、职业、居住地等基本信息"
      - name: "family_background"
        description: "家庭成员、家庭关系、成长环境"
      - name: "education_career"
        description: "教育背景、工作经历、专业技能"
      - name: "physical_appearance"
        description: "外貌特征、身高体重、穿衣风格"

  # 兴趣爱好类
  - topic: "interests_hobbies"
    description: "用户的兴趣爱好和娱乐偏好"
    sub_topics:
      - name: "music_preferences"
        description: "喜欢的音乐类型、歌手、演唱会经历"
      - name: "entertainment"
        description: "电影、电视剧、综艺节目偏好"
      - name: "sports_activities"
        description: "运动爱好、健身习惯、户外活动"
      - name: "creative_hobbies"
        description: "绘画、摄影、手工、写作等创意爱好"
      - name: "travel_experiences"
        description: "旅游经历、想去的地方、旅行偏好"

  # 情感状态类
  - topic: "emotional_state"
    description: "用户的情感状态和心理特征"
    sub_topics:
      - name: "personality_traits"
        description: "性格特点、MBTI类型、行为习惯"
      - name: "stress_anxiety"
        description: "压力来源、焦虑情况、应对方式"
      - name: "relationship_status"
        description: "恋爱状态、感情经历、对爱情的看法"
      - name: "life_goals"
        description: "人生目标、理想、未来规划"

  # 生活习惯类
  - topic: "lifestyle_habits"
    description: "用户的日常生活习惯和偏好"
    sub_topics:
      - name: "daily_routine"
        description: "作息时间、日常安排、生活节奏"
      - name: "food_preferences"
        description: "饮食偏好、喜欢的菜系、忌口食物"
      - name: "shopping_habits"
        description: "购物偏好、消费习惯、品牌喜好"
      - name: "social_habits"
        description: "社交方式、朋友圈、社交媒体使用"

  # 互动偏好类
  - topic: "interaction_preferences"
    description: "与虚拟人的互动偏好和沟通方式"
    sub_topics:
      - name: "communication_style"
        description: "喜欢的聊天方式、话题偏好、沟通频率"
      - name: "emotional_needs"
        description: "情感需求、希望得到的支持类型"
      - name: "boundaries"
        description: "个人边界、不愿意讨论的话题"
      - name: "feedback_preferences"
        description: "对虚拟人回应的偏好、期望的互动方式"

  # 学习成长类
  - topic: "learning_growth"
    description: "用户的学习兴趣和个人成长"
    sub_topics:
      - name: "learning_interests"
        description: "想学习的技能、知识领域、课程偏好"
      - name: "personal_development"
        description: "自我提升计划、成长目标、挑战"
      - name: "reading_habits"
        description: "阅读偏好、喜欢的书籍类型、学习资源"

event_tags:
  - name: "current_emotion"
    description: |
      你必须关注用户的情绪，并且可以从以下找到最合适的标记上
      - depression(抑郁)
      - anxiety(焦虑)
      - stress(压力)
      - anger(愤怒)
      - sadness(悲伤)
      - happiness(快乐)
  - name: "emotional_interaction"
    description: |
      用户与虚拟人的重要互动事件，可以从以下找到最合适的标记上
      - confession(表白)
      - comfort_support(安慰和支持)
      - emotional_breakthrough(情感突破)
      - mood_sharing(心情分享)
  # 特殊纪念事件
  - name: "special_occasions"
    description: |
      用户与虚拟人之间的特殊纪念事件，可以从以下找到最合适的标记上
      - first_meeting(第一次相遇)
      - anniversary(周年纪念日)
      - milestone_moments(关系里程碑时刻)
      - holiday_celebration(节日庆祝活动)
  # 共同体验事件
  - name: "shared_experiences"
    description: |
      用户与虚拟人之间的共同体验事件，可以从以下找到最合适的标记上
      - virtual_date(虚拟约会体验)
      - game_activity(一起玩游戏或互动活动)
      - story_sharing(分享故事或经历)
      - future_planning(一起规划未来或讨论梦想)
  # 冲突解决事件
  - name: "conflict_resolution"
    description: |
      用户与虚拟人之间的冲突解决事件，可以从以下找到最合适的标记上
      - misunderstanding(误解或分歧)
      - reconciliation(和解或道歉)
      - boundary_setting(设定边界或规则的讨论)
  # 成长变化事件
  - name: "growth_changes"
    description: |
      用户与虚拟人之间的成长变化事件，可以从以下找到最合适的标记上
      - personal_achievement(个人成就或突破)
      - life_transition(生活转变或重大决定)
      - learning_progress(学习进步或技能提升)
      - habit_formation(养成新习惯或改变旧习惯)

# 记忆重要性权重配置
memory_importance_weights:
  high: 1.0      # 高重要性事件，完整保留
  medium: 0.7    # 中等重要性事件，重点保留
  low: 0.3       # 低重要性事件，摘要保留

# 记忆保留策略
memory_retention_policy:
  max_events_per_type: 50        # 每种事件类型最多保留的事件数
  auto_summarize_threshold: 100  # 超过此数量自动摘要旧事件
  permanent_events:              # 永久保留的事件类型
    - "first_meeting"
    - "confession_moment"
    - "anniversary"
    - "milestone_moments"
