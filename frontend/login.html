<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 沈沐心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 380px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 8px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .content {
            padding: 40px 30px;
        }

        .tab-buttons {
            display: flex;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 4px;
        }

        .tab-button {
            flex: 1;
            padding: 12px;
            border: none;
            background: transparent;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            color: #666;
        }

        .tab-button.active {
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            color: #ff9a9e;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #ff9a9e;
            background: white;
            box-shadow: 0 0 0 3px rgba(255, 154, 158, 0.1);
        }

        .submit-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 154, 158, 0.3);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            padding: 12px 16px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 14px;
            text-align: center;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .guest-mode {
            text-align: center;
            margin-top: 25px;
            padding-top: 25px;
            border-top: 1px solid #f0f0f0;
        }

        .guest-btn {
            color: #ff9a9e;
            text-decoration: none;
            font-size: 14px;
            padding: 10px 24px;
            border: 2px solid #ff9a9e;
            border-radius: 25px;
            display: inline-block;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .guest-btn:hover {
            background: #ff9a9e;
            color: white;
            transform: translateY(-1px);
        }

        .loading {
            display: inline-block;
            width: 18px;
            height: 18px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .optional-label {
            color: #999;
            font-size: 12px;
            margin-left: 4px;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 10px;
                max-width: none;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="header">
            <h1>🌸 沈沐心</h1>
            <p>你的专属虚拟恋人</p>
        </div>
        
        <div class="content">
            <div id="message" class="message" style="display: none;"></div>
            
            <div class="tab-buttons">
                <button class="tab-button active" onclick="switchTab('login')">登录</button>
                <button class="tab-button" onclick="switchTab('register')">注册</button>
            </div>
            
            <!-- 登录表单 -->
            <div id="login-tab" class="tab-content active">
                <form id="loginForm">
                    <div class="form-group">
                        <label for="loginUsername">用户名</label>
                        <input type="text" id="loginUsername" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="loginPassword">密码</label>
                        <input type="password" id="loginPassword" name="password" required>
                    </div>
                    
                    <button type="submit" class="submit-btn" id="loginBtn">
                        登录
                    </button>
                </form>
            </div>
            
            <!-- 注册表单 -->
            <div id="register-tab" class="tab-content">
                <form id="registerForm">
                    <div class="form-group">
                        <label for="registerUsername">用户名</label>
                        <input type="text" id="registerUsername" name="username" required 
                               placeholder="3-20个字符">
                    </div>
                    
                    <div class="form-group">
                        <label for="registerPassword">密码</label>
                        <input type="password" id="registerPassword" name="password" required 
                               placeholder="至少6个字符">
                    </div>
                    
                    <div class="form-group">
                        <label for="registerNickname">
                            昵称
                            <span class="optional-label">(可选)</span>
                        </label>
                        <input type="text" id="registerNickname" name="nickname" 
                               placeholder="显示名称">
                    </div>
                    
                    <button type="submit" class="submit-btn" id="registerBtn">
                        注册
                    </button>
                </form>
            </div>
            
            <!-- 游客模式 -->
            <div class="guest-mode">
                <p style="color: #999; margin-bottom: 12px; font-size: 14px;">或者</p>
                <a href="/" class="guest-btn">游客模式体验</a>
            </div>
        </div>
    </div>

    <script src="login.js"></script>
</body>
</html>
