* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

/* 布局 */
.admin-layout {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏 */
.sidebar {
    width: 260px;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    font-size: 24px;
}

.logo-text {
    font-size: 18px;
    font-weight: 600;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: 4px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-left-color: white;
}

.nav-icon {
    font-size: 18px;
    width: 20px;
    text-align: center;
}

.nav-text {
    font-size: 14px;
    font-weight: 500;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.back-to-chat {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.back-to-chat:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

/* 主内容区 */
.main-content {
    flex: 1;
    margin-left: 260px;
    display: flex;
    flex-direction: column;
}

/* 顶部栏 */
.topbar {
    background: white;
    padding: 0 24px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.sidebar-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.sidebar-toggle span {
    width: 20px;
    height: 2px;
    background: #333;
    transition: all 0.3s ease;
}

.page-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-name {
    font-size: 14px;
    color: #666;
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.page-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.page-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 按钮 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

/* 卡片 */
.content-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 24px;
    margin-bottom: 24px;
}

/* 仪表盘 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

.dashboard-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.chart-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #333;
}

/* 表格 */
.search-bar {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.search-input {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
}

.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* 表单 */
.form-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.form-select:focus {
    outline: none;
    border-color: #667eea;
}

/* 状态 */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: #999;
}

.status-value {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-value.online {
    background: #d4edda;
    color: #155724;
}

/* 记忆相关样式 */
.memory-stats {
    display: flex;
    gap: 24px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.memory-list {
    max-height: 500px;
    overflow-y: auto;
}

.memory-item {
    padding: 16px;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 12px;
    background: #fafafa;
}

.memory-item:hover {
    background: #f0f0f0;
}

.memory-content {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 8px;
}

.memory-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
}

/* 活动列表 */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    padding: 12px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.activity-desc {
    font-size: 12px;
    color: #666;
}

.activity-time {
    font-size: 12px;
    color: #999;
}

/* 系统状态 */
.system-status {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.status-label {
    font-size: 14px;
    color: #333;
}

/* 人设配置 */
.persona-config {
    display: grid;
    gap: 20px;
}

.config-section {
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 8px;
    background: #fafafa;
}

.config-section h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.config-item {
    margin-bottom: 12px;
}

.config-label {
    font-size: 14px;
    font-weight: 500;
    color: #555;
    margin-bottom: 4px;
}

.config-value {
    font-size: 14px;
    color: #333;
    background: white;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

/* Memobase ID 样式 */
.memobase-id {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
}

.memobase-uuid {
    background: #f0f8ff;
    color: #2563eb;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #bfdbfe;
    cursor: help;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 11px;
}

.memobase-uuid:hover {
    background: #dbeafe;
    border-color: #93c5fd;
}

.status-unmapped {
    background: #fef2f2;
    color: #dc2626;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #fecaca;
    font-size: 11px;
    font-weight: 500;
}

/* 记忆管理选项卡样式 */
.memory-tabs {
    display: flex;
    border-bottom: 2px solid #f0f0f0;
    margin-bottom: 24px;
}

.tab-btn {
    padding: 12px 24px;
    border: none;
    background: transparent;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: #667eea;
    background: #f8f9fa;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: #f8f9fa;
}

.memory-tab-content {
    display: none;
}

.memory-tab-content.active {
    display: block;
}

/* 记忆控制面板样式 */
.memory-controls {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.control-group:last-child {
    margin-bottom: 0;
}

.control-group label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    min-width: 80px;
}

.control-group .form-select {
    min-width: 200px;
}

.control-group .search-input {
    flex: 1;
    max-width: 300px;
}

/* 记忆项样式增强 */
.memory-item {
    padding: 16px;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 12px;
    background: #fafafa;
    transition: all 0.3s ease;
}

.memory-item:hover {
    background: #f0f0f0;
    border-color: #ddd;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.memory-content {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
    color: #333;
}

.memory-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #666;
    border-top: 1px solid #eee;
    padding-top: 8px;
}

.memory-category {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.memory-importance {
    font-weight: 500;
}

.memory-importance.high {
    color: #d32f2f;
}

.memory-importance.medium {
    color: #f57c00;
}

.memory-importance.low {
    color: #388e3c;
}

.memory-actions {
    display: flex;
    gap: 8px;
}

.memory-actions .btn {
    padding: 4px 8px;
    font-size: 12px;
}

/* 记忆统计样式增强 */
.memory-stats {
    display: flex;
    gap: 24px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    flex: 1;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

/* Memobase管理页面样式 */
.memobase-status {
    margin-bottom: 20px;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.status-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.status-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.status-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.status-value.online {
    color: #28a745;
}

.status-value.offline {
    color: #dc3545;
}

.status-value.warning {
    color: #ffc107;
}

/* Memobase控制面板 */
.memobase-controls {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.memobase-controls .control-group {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.memobase-controls .control-group:last-child {
    margin-bottom: 0;
}

/* Memobase选项卡 */
.memobase-tabs {
    display: flex;
    border-bottom: 2px solid #f0f0f0;
    margin-bottom: 24px;
}

.memobase-tabs .tab-btn {
    padding: 12px 24px;
    border: none;
    background: transparent;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.memobase-tabs .tab-btn:hover {
    color: #667eea;
    background: #f8f9fa;
}

.memobase-tabs .tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: #f8f9fa;
}

.memobase-tab-content {
    display: none;
}

.memobase-tab-content.active {
    display: block;
}

/* 选项卡控制 */
.tab-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.tab-info {
    font-size: 12px;
    color: #666;
    font-style: italic;
}

/* 数据显示区域 */
.data-display {
    min-height: 200px;
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 16px;
    background: #fafafa;
}

.data-display pre {
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.data-display .json-data {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 12px;
}

.data-display .json-key {
    color: #0066cc;
    font-weight: 600;
}

.data-display .json-string {
    color: #009900;
}

.data-display .json-number {
    color: #cc6600;
}

.data-display .json-boolean {
    color: #cc0066;
}

/* 用户详情 */
.memobase-user-details {
    margin-top: 20px;
}

.user-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 12px;
    background: #fafafa;
}

.user-detail-label {
    font-weight: 600;
    color: #333;
}

.user-detail-value {
    color: #666;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
}

/* 系统操作 */
.system-operations {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.operation-group {
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 8px;
    background: #fafafa;
}

.operation-group h4 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.operation-group .btn {
    margin-right: 8px;
    margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .status-grid {
        grid-template-columns: 1fr;
    }

    .memobase-controls .control-group {
        flex-direction: column;
        align-items: stretch;
    }

    .tab-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .system-operations {
        grid-template-columns: 1fr;
    }
}

/* 响应式 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar-toggle {
        display: flex;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-charts {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .search-bar {
        flex-direction: column;
    }
}
