class AdminManager {
    constructor() {
        this.currentPage = 'dashboard';
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadDashboard();
    }

    bindEvents() {
        // 导航菜单点击事件
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.page;
                this.switchPage(page);
            });
        });

        // 侧边栏切换
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                document.querySelector('.sidebar').classList.toggle('open');
            });
        }
    }

    switchPage(page) {
        // 更新导航状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-page="${page}"]`).classList.add('active');

        // 更新页面内容
        document.querySelectorAll('.page-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${page}-page`).classList.add('active');

        // 更新页面标题
        const titles = {
            dashboard: '仪表盘',
            users: '用户管理',
            memories: '记忆管理',
            persona: '人设管理',
            memobase: 'Memobase管理',
            config: '系统配置'
        };
        document.getElementById('pageTitle').textContent = titles[page];

        this.currentPage = page;

        // 加载页面数据
        this.loadPageData(page);
    }

    async loadPageData(page) {
        switch (page) {
            case 'dashboard':
                await this.loadDashboard();
                break;
            case 'users':
                await this.loadUsers();
                break;
            case 'memories':
                await this.loadMemories();
                break;
            case 'persona':
                await this.loadPersonaConfig();
                break;
            case 'memobase':
                await this.loadMemobase();
                break;
            case 'config':
                await this.loadSystemConfig();
                break;
        }
    }

    async loadDashboard() {
        try {
            // 加载统计数据
            const statsResponse = await fetch('/api/admin/stats');
            if (statsResponse.ok) {
                const stats = await statsResponse.json();
                this.updateDashboardStats(stats);
            }

            // 加载最近活动
            this.loadRecentActivity();
        } catch (error) {
            console.error('加载仪表盘数据失败:', error);
        }
    }

    updateDashboardStats(stats) {
        document.getElementById('totalUsers').textContent = stats.total_users || 0;
        document.getElementById('totalConversations').textContent = stats.total_conversations || 0;
        document.getElementById('totalMemories').textContent = stats.total_memories || 0;
        document.getElementById('avgAffection').textContent = stats.avg_affection || 0;
    }

    async loadRecentActivity() {
        const activityList = document.getElementById('recentActivity');
        try {
            const response = await fetch('/api/admin/activity');
            if (response.ok) {
                const activities = await response.json();
                this.renderActivityList(activities, activityList);
            } else {
                activityList.innerHTML = '<div class="empty-state">暂无活动记录</div>';
            }
        } catch (error) {
            activityList.innerHTML = '<div class="empty-state">加载失败</div>';
        }
    }

    renderActivityList(activities, container) {
        if (!activities || activities.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无活动记录</div>';
            return;
        }

        const html = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-desc">${activity.description}</div>
                </div>
                <div class="activity-time">${this.formatTime(activity.timestamp)}</div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    async loadUsers() {
        const tableBody = document.getElementById('usersTableBody');
        tableBody.innerHTML = '<tr><td colspan="7" class="loading">加载中...</td></tr>';

        try {
            const response = await fetch('/api/admin/users');
            if (response.ok) {
                const users = await response.json();
                this.renderUsersTable(users, tableBody);
            } else {
                tableBody.innerHTML = '<tr><td colspan="8" class="empty-state">加载失败</td></tr>';
            }
        } catch (error) {
            tableBody.innerHTML = '<tr><td colspan="8" class="empty-state">加载失败</td></tr>';
        }
    }

    renderUsersTable(users, tableBody) {
        if (!users || users.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="8" class="empty-state">暂无用户</td></tr>';
            return;
        }

        const html = users.map(user => `
            <tr>
                <td>${user.user_id}</td>
                <td>${user.username}</td>
                <td>${user.nickname || '-'}</td>
                <td class="memobase-id">${this.formatMemobaseId(user.memobase_id)}</td>
                <td>${this.formatDate(user.created_at)}</td>
                <td>${this.formatDate(user.last_active)}</td>
                <td>${user.affection_level || 0}</td>
                <td>
                    <button class="btn btn-secondary" onclick="adminManager.viewUserMemories('${user.user_id}')">
                        查看记忆
                    </button>
                </td>
            </tr>
        `).join('');

        tableBody.innerHTML = html;
    }

    async loadMemories() {
        try {
            // 加载用户列表到选择器
            const response = await fetch('/api/admin/users');
            if (response.ok) {
                const users = await response.json();
                this.populateUserSelect(users);
            }

            // 加载记忆统计
            await this.refreshMemoryStats();
        } catch (error) {
            console.error('加载记忆管理数据失败:', error);
        }
    }

    async refreshMemoryStats() {
        try {
            const statsResponse = await fetch('/api/admin/memories/stats');
            if (statsResponse.ok) {
                const stats = await statsResponse.json();
                this.updateMemoryStats(stats);
            }
        } catch (error) {
            console.error('刷新记忆统计失败:', error);
        }
    }

    populateUserSelect(users) {
        const select = document.getElementById('memoryUserSelect');
        const options = users.map(user => 
            `<option value="${user.user_id}">${user.username} (${user.nickname || '无昵称'})</option>`
        ).join('');
        select.innerHTML = '<option value="">选择用户</option>' + options;
    }

    updateMemoryStats(stats) {
        document.getElementById('userMemoryCount').textContent = stats.user_memories?.total || 0;
        document.getElementById('personaMemoryCount').textContent = stats.persona_memories?.total || 0;
        document.getElementById('activeUserCount').textContent = stats.active_users || 0;
    }

    async loadUserMemories() {
        const userId = document.getElementById('memoryUserSelect').value;
        if (!userId) {
            alert('请先选择用户');
            return;
        }

        const memoryList = document.getElementById('userMemoryList');
        memoryList.innerHTML = '<div class="loading">加载中...</div>';

        try {
            const response = await fetch(`/api/admin/memories/user/${userId}?limit=50`);
            if (response.ok) {
                const result = await response.json();
                this.renderUserMemoryList(result.memories, memoryList);
            } else {
                memoryList.innerHTML = '<div class="empty-state">加载失败</div>';
            }
        } catch (error) {
            console.error('加载用户记忆失败:', error);
            memoryList.innerHTML = '<div class="empty-state">加载失败</div>';
        }
    }

    async loadPersonaMemories() {
        const category = document.getElementById('personaMemoryCategory').value;
        const memoryList = document.getElementById('personaMemoryList');
        memoryList.innerHTML = '<div class="loading">加载中...</div>';

        try {
            let url = '/api/admin/memories/persona?limit=50';
            if (category) {
                url += `&category=${category}`;
            }

            const response = await fetch(url);
            if (response.ok) {
                const result = await response.json();
                this.renderPersonaMemoryList(result.memories, memoryList);
            } else {
                memoryList.innerHTML = '<div class="empty-state">加载失败</div>';
            }
        } catch (error) {
            console.error('加载虚拟人记忆失败:', error);
            memoryList.innerHTML = '<div class="empty-state">加载失败</div>';
        }
    }

    renderUserMemoryList(memories, container) {
        if (!memories || memories.length === 0) {
            container.innerHTML = '<div class="empty-state">该用户暂无记忆</div>';
            return;
        }

        const html = memories.map(memory => `
            <div class="memory-item">
                <div class="memory-content">${memory.content || memory.text || '无内容'}</div>
                <div class="memory-meta">
                    <div class="memory-info">
                        <span class="memory-importance ${this.getImportanceClass(memory.importance)}">
                            重要性: ${memory.importance || 'N/A'}
                        </span>
                        <span class="memory-type">类型: 用户记忆</span>
                    </div>
                    <div class="memory-time">
                        ${this.formatDate(memory.timestamp || memory.created_at)}
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    renderPersonaMemoryList(memories, container) {
        if (!memories || memories.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无虚拟人记忆</div>';
            return;
        }

        const html = memories.map(memory => `
            <div class="memory-item">
                <div class="memory-content">${memory.content || memory.text || '无内容'}</div>
                <div class="memory-meta">
                    <div class="memory-info">
                        <span class="memory-category">${memory.category || '未分类'}</span>
                        <span class="memory-importance ${this.getImportanceClass(memory.importance)}">
                            重要性: ${memory.importance || 'N/A'}
                        </span>
                        <span class="memory-type">类型: 虚拟人记忆</span>
                    </div>
                    <div class="memory-actions">
                        <button class="btn btn-secondary" onclick="adminManager.editPersonaMemory('${memory.id}')">编辑</button>
                        <button class="btn btn-danger" onclick="adminManager.deletePersonaMemory('${memory.id}')">删除</button>
                    </div>
                    <div class="memory-time">
                        ${this.formatDate(memory.timestamp || memory.created_at)}
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    getImportanceClass(importance) {
        if (!importance) return '';
        const level = importance.toString().toLowerCase();
        if (level.includes('high') || level.includes('高')) return 'high';
        if (level.includes('medium') || level.includes('中')) return 'medium';
        if (level.includes('low') || level.includes('低')) return 'low';
        return '';
    }

    async loadPersonaConfig() {
        const configContainer = document.getElementById('personaConfig');
        configContainer.innerHTML = '<div class="loading">加载中...</div>';

        try {
            const response = await fetch('/api/admin/config/persona');
            if (response.ok) {
                const config = await response.json();
                this.renderPersonaConfig(config, configContainer);
            } else {
                configContainer.innerHTML = '<div class="empty-state">加载失败</div>';
            }
        } catch (error) {
            configContainer.innerHTML = '<div class="empty-state">加载失败</div>';
        }
    }

    renderPersonaConfig(config, container) {
        // 这里可以根据实际的配置结构来渲染
        const html = `
            <div class="config-section">
                <h4>基本信息</h4>
                <div class="config-item">
                    <div class="config-label">姓名</div>
                    <div class="config-value">${config.name || '沈沐心'}</div>
                </div>
                <div class="config-item">
                    <div class="config-label">年龄</div>
                    <div class="config-value">${config.age || '25岁'}</div>
                </div>
                <div class="config-item">
                    <div class="config-label">职业</div>
                    <div class="config-value">${config.profession || '心理咨询师'}</div>
                </div>
            </div>
            <div class="config-section">
                <h4>性格特征</h4>
                <div class="config-item">
                    <div class="config-label">性格描述</div>
                    <div class="config-value">${config.personality || '温柔、善解人意、专业'}</div>
                </div>
            </div>
        `;
        container.innerHTML = html;
    }

    async loadSystemConfig() {
        const configForm = document.getElementById('configForm');
        configForm.innerHTML = '<div class="loading">加载中...</div>';

        // 这里可以加载系统配置
        setTimeout(() => {
            configForm.innerHTML = '<div class="empty-state">系统配置功能开发中...</div>';
        }, 1000);
    }

    async loadMemobase() {
        try {
            // 加载用户列表到选择器
            const response = await fetch('/api/admin/users');
            if (response.ok) {
                const users = await response.json();
                this.populateMemobaseUserSelect(users);
            }

            // 加载Memobase状态
            await this.refreshMemobaseStatus();
        } catch (error) {
            console.error('加载Memobase管理数据失败:', error);
        }
    }

    async refreshMemobaseStatus() {
        try {
            // 通过代理调用Memobase健康检查API
            const response = await fetch('/api/memobase/v1/healthcheck');
            if (response.ok) {
                const healthData = await response.json();

                // 根据OpenAPI文档，没有用户列表API，显示映射用户数
                let totalUsers = '不可用';
                try {
                    const usersResponse = await fetch('/api/admin/users');
                    if (usersResponse.ok) {
                        const users = await usersResponse.json();
                        const mappedUsers = users.filter(u => u.memobase_id);
                        totalUsers = `${mappedUsers.length} (已映射)`;
                    }
                } catch (e) {
                    console.warn('获取用户统计失败:', e);
                }

                this.updateMemobaseStatus({
                    connection: 'online',
                    version: healthData.version || 'Unknown',
                    total_users: totalUsers,
                    url: 'http://localhost:8019',
                    health: healthData
                });
            } else {
                this.updateMemobaseStatus({
                    connection: 'offline',
                    error: `HTTP ${response.status}: ${response.statusText}`
                });
            }
        } catch (error) {
            console.error('获取Memobase状态失败:', error);
            this.updateMemobaseStatus({
                connection: 'offline',
                error: error.message
            });
        }
    }

    populateMemobaseUserSelect(users) {
        const select = document.getElementById('memobaseUserSelect');
        select.innerHTML = '<option value="">选择用户</option>';

        users.forEach(user => {
            const option = document.createElement('option');
            option.value = user.user_id;
            option.textContent = `${user.username || user.user_id} (${user.memobase_id || '未映射'})`;
            select.appendChild(option);
        });
    }

    updateMemobaseStatus(status) {
        // 更新连接状态
        const connectionEl = document.getElementById('memobaseConnection');
        if (status.connection === 'online') {
            connectionEl.textContent = '已连接';
            connectionEl.className = 'status-value online';
        } else {
            connectionEl.textContent = '连接失败';
            connectionEl.className = 'status-value offline';
        }

        // 更新其他状态信息
        document.getElementById('memobaseVersion').textContent = status.version || '-';
        document.getElementById('memobaseTotalUsers').textContent = status.total_users || '-';
        document.getElementById('memobaseUrl').textContent = status.url || 'http://localhost:8019';

        if (status.error) {
            console.error('Memobase状态错误:', status.error);
        }
    }

    async loadMemobaseUserDetails(userId) {
        const detailsContainer = document.getElementById('memobaseUserDetails');
        detailsContainer.innerHTML = '<div class="loading">加载中...</div>';

        try {
            // 获取用户的Memobase UUID
            const memobaseUuid = await this.getMemobaseUuid(userId);
            if (!memobaseUuid) {
                detailsContainer.innerHTML = '<div class="empty-state">用户未映射到Memobase</div>';
                return;
            }

            // 通过代理调用Memobase API获取用户详情
            const memobaseResponse = await fetch(`/api/memobase/v1/users/${memobaseUuid}`);
            if (memobaseResponse.ok) {
                const memobaseUser = await memobaseResponse.json();

                // 获取用户的blobs数量
                let blobCount = 0;
                try {
                    const blobsResponse = await fetch(`/api/memobase/v1/users/blobs/${memobaseUuid}/chat`);
                    if (blobsResponse.ok) {
                        const blobs = await blobsResponse.json();
                        // Memobase返回格式: {"data":{"ids":[...]},"errno":0,"errmsg":""}
                        const blobData = blobs.data || blobs;
                        blobCount = Array.isArray(blobData.ids) ? blobData.ids.length : 0;
                    }
                } catch (e) {
                    console.warn('获取blobs数量失败:', e);
                }

                // 获取用户画像数量
                let profileCount = 0;
                try {
                    const profileResponse = await fetch(`/api/memobase/v1/users/profile/${memobaseUuid}`);
                    if (profileResponse.ok) {
                        const profile = await profileResponse.json();
                        profileCount = Array.isArray(profile) ? profile.length : 0;
                    }
                } catch (e) {
                    console.warn('获取画像数量失败:', e);
                }

                const details = {
                    user_id: userId,
                    memobase_uuid: memobaseUuid,
                    created_at: memobaseUser.created_at,
                    updated_at: memobaseUser.updated_at,
                    blob_count: blobCount,
                    profile_count: profileCount,
                    memobase_data: memobaseUser
                };

                this.renderMemobaseUserDetails(details);
            } else {
                detailsContainer.innerHTML = '<div class="empty-state">无法获取Memobase用户信息</div>';
            }
        } catch (error) {
            console.error('加载用户详情失败:', error);
            detailsContainer.innerHTML = `<div class="empty-state">加载失败: ${error.message}</div>`;
        }
    }

    renderMemobaseUserDetails(details) {
        const container = document.getElementById('memobaseUserDetails');
        const html = `
            <div class="user-detail-item">
                <span class="user-detail-label">用户ID</span>
                <span class="user-detail-value">${details.user_id || '-'}</span>
            </div>
            <div class="user-detail-item">
                <span class="user-detail-label">Memobase UUID</span>
                <span class="user-detail-value">${details.memobase_uuid || '-'}</span>
            </div>
            <div class="user-detail-item">
                <span class="user-detail-label">创建时间</span>
                <span class="user-detail-value">${this.formatDate(details.created_at)}</span>
            </div>
            <div class="user-detail-item">
                <span class="user-detail-label">最后更新</span>
                <span class="user-detail-value">${this.formatDate(details.updated_at)}</span>
            </div>
            <div class="user-detail-item">
                <span class="user-detail-label">聊天记录数</span>
                <span class="user-detail-value">${details.blob_count || 0}</span>
            </div>
            <div class="user-detail-item">
                <span class="user-detail-label">画像项数</span>
                <span class="user-detail-value">${details.profile_count || 0}</span>
            </div>
        `;
        container.innerHTML = html;
    }

    async flushUserMemory(userId) {
        try {
            // 获取用户的Memobase UUID
            const memobaseUuid = await this.getMemobaseUuid(userId);
            if (!memobaseUuid) {
                alert('用户未映射到Memobase');
                return;
            }

            // 根据OpenAPI文档，正确的刷新记忆API路径
            const response = await fetch(`/api/memobase/v1/users/buffer/${memobaseUuid}/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                alert('用户记忆刷新成功');
                console.log('刷新结果:', result);
                // 重新加载用户详情
                this.loadMemobaseUserDetails(userId);
            } else {
                alert(`刷新失败: HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('刷新用户记忆失败:', error);
            alert(`刷新失败: ${error.message}`);
        }
    }

    async loadUserBlobs(userId) {
        const container = document.getElementById('blobsData');
        container.innerHTML = '<div class="loading">加载中...</div>';

        try {
            // 获取用户的Memobase UUID
            const memobaseUuid = await this.getMemobaseUuid(userId);
            if (!memobaseUuid) {
                container.innerHTML = '<div class="empty-state">用户未映射到Memobase</div>';
                return;
            }

            // 根据OpenAPI文档，正确的blobs API路径是 /api/v1/users/blobs/{user_id}/{blob_type}
            // 使用chat类型获取聊天记录
            const response = await fetch(`/api/memobase/v1/users/blobs/${memobaseUuid}/chat`);
            if (response.ok) {
                const data = await response.json();
                this.renderDataDisplay(container, data, 'Blobs数据 (聊天记录)');
            } else if (response.status === 404) {
                container.innerHTML = '<div class="empty-state">该用户暂无聊天记录(Blobs)</div>';
            } else {
                container.innerHTML = `<div class="empty-state">加载失败: HTTP ${response.status}</div>`;
            }
        } catch (error) {
            console.error('加载Blobs失败:', error);
            container.innerHTML = `<div class="empty-state">加载失败: ${error.message}</div>`;
        }
    }

    async loadUserProfile(userId) {
        const container = document.getElementById('profileData');
        container.innerHTML = '<div class="loading">加载中...</div>';

        try {
            // 获取用户的Memobase UUID
            const memobaseUuid = await this.getMemobaseUuid(userId);
            if (!memobaseUuid) {
                container.innerHTML = '<div class="empty-state">用户未映射到Memobase</div>';
                return;
            }

            // 通过代理调用Memobase API
            const response = await fetch(`/api/memobase/v1/users/profile/${memobaseUuid}`);
            if (response.ok) {
                const data = await response.json();
                this.renderDataDisplay(container, data, '用户画像数据');
            } else {
                container.innerHTML = '<div class="empty-state">加载失败</div>';
            }
        } catch (error) {
            console.error('加载用户画像失败:', error);
            container.innerHTML = `<div class="empty-state">加载失败: ${error.message}</div>`;
        }
    }

    async loadUserContext(userId, tokenSize) {
        const container = document.getElementById('contextData');
        container.innerHTML = '<div class="loading">加载中...</div>';

        try {
            // 获取用户的Memobase UUID
            const memobaseUuid = await this.getMemobaseUuid(userId);
            if (!memobaseUuid) {
                container.innerHTML = '<div class="empty-state">用户未映射到Memobase</div>';
                return;
            }

            // 通过代理调用Memobase API
            const response = await fetch(`/api/memobase/v1/users/profile/${memobaseUuid}?max_token_size=${tokenSize}`);
            if (response.ok) {
                const data = await response.json();
                this.renderDataDisplay(container, data, '用户上下文数据');
            } else {
                container.innerHTML = '<div class="empty-state">加载失败</div>';
            }
        } catch (error) {
            console.error('加载用户上下文失败:', error);
            container.innerHTML = `<div class="empty-state">加载失败: ${error.message}</div>`;
        }
    }

    // 辅助方法：获取用户的Memobase UUID
    async getMemobaseUuid(userId) {
        try {
            const userResponse = await fetch('/api/admin/users');
            if (!userResponse.ok) {
                throw new Error('无法获取用户列表');
            }

            const users = await userResponse.json();
            const user = users.find(u => u.user_id === userId);
            return user?.memobase_id || null;
        } catch (error) {
            console.error('获取Memobase UUID失败:', error);
            return null;
        }
    }

    renderDataDisplay(container, data, title) {
        let html = `<h4>${title}</h4>`;

        if (typeof data === 'string') {
            html += `<pre>${data}</pre>`;
        } else if (Array.isArray(data)) {
            html += `<div class="json-data">数组长度: ${data.length}</div>`;
            data.forEach((item, index) => {
                html += `<div class="json-data">
                    <strong>项目 ${index + 1}:</strong>
                    <pre>${JSON.stringify(item, null, 2)}</pre>
                </div>`;
            });
        } else if (typeof data === 'object') {
            html += `<div class="json-data">
                <pre>${JSON.stringify(data, null, 2)}</pre>
            </div>`;
        } else {
            html += `<div class="json-data">${data}</div>`;
        }

        container.innerHTML = html;
    }

    async flushAllUserMemories() {
        try {
            // 获取所有映射用户
            const usersResponse = await fetch('/api/admin/users');
            if (!usersResponse.ok) {
                alert('获取用户列表失败');
                return;
            }

            const users = await usersResponse.json();
            const mappedUsers = users.filter(u => u.memobase_id);

            if (mappedUsers.length === 0) {
                alert('没有找到已映射的用户');
                return;
            }

            let successCount = 0;
            let failCount = 0;

            // 遍历每个用户执行刷新
            for (const user of mappedUsers) {
                try {
                    const response = await fetch(`/api/memobase/v1/users/buffer/${user.memobase_id}/chat`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });

                    if (response.ok) {
                        successCount++;
                    } else {
                        failCount++;
                        console.warn(`刷新用户 ${user.user_id} 失败: HTTP ${response.status}`);
                    }
                } catch (e) {
                    failCount++;
                    console.error(`刷新用户 ${user.user_id} 失败:`, e);
                }
            }

            alert(`批量刷新完成:\n成功: ${successCount} 个用户\n失败: ${failCount} 个用户\n总计: ${mappedUsers.length} 个用户`);

        } catch (error) {
            console.error('批量刷新失败:', error);
            alert(`批量刷新失败: ${error.message}`);
        }
    }

    async exportMemobaseData() {
        try {
            // 获取所有映射用户
            const usersResponse = await fetch('/api/admin/users');
            if (!usersResponse.ok) {
                alert('获取用户列表失败');
                return;
            }

            const users = await usersResponse.json();
            const mappedUsers = users.filter(u => u.memobase_id);

            const exportData = {
                export_time: new Date().toISOString(),
                total_users: mappedUsers.length,
                users: []
            };

            // 遍历每个用户获取数据
            for (const user of mappedUsers) {
                try {
                    const userData = {
                        user_id: user.user_id,
                        memobase_uuid: user.memobase_id,
                        blobs: [],
                        profile: []
                    };

                    // 获取用户详情
                    const userResponse = await fetch(`/api/memobase/v1/users/${user.memobase_id}`);
                    if (userResponse.ok) {
                        userData.user_info = await userResponse.json();
                    }

                    // 获取blobs
                    const blobsResponse = await fetch(`/api/memobase/v1/users/blobs/${user.memobase_id}/chat`);
                    if (blobsResponse.ok) {
                        userData.blobs = await blobsResponse.json();
                    }

                    // 获取profile
                    const profileResponse = await fetch(`/api/memobase/v1/users/profile/${user.memobase_id}`);
                    if (profileResponse.ok) {
                        userData.profile = await profileResponse.json();
                    }

                    exportData.users.push(userData);
                } catch (e) {
                    console.warn(`导出用户 ${user.user_id} 数据失败:`, e);
                }
            }

            // 创建下载
            const jsonStr = JSON.stringify(exportData, null, 2);
            const blob = new Blob([jsonStr], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `memobase_export_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            alert(`导出完成！\n导出了 ${exportData.users.length} 个用户的数据`);

        } catch (error) {
            console.error('导出失败:', error);
            alert(`导出失败: ${error.message}`);
        }
    }

    async getMemobaseStats() {
        try {
            // 获取本地用户映射统计
            const usersResponse = await fetch('/api/admin/users');
            if (!usersResponse.ok) {
                alert('获取用户列表失败');
                return;
            }

            const users = await usersResponse.json();
            const mappedUsers = users.filter(u => u.memobase_id);

            let totalBlobs = 0;
            let totalProfiles = 0;

            // 遍历每个映射用户获取统计
            for (const user of mappedUsers) {
                try {
                    // 获取blobs数量
                    const blobsResponse = await fetch(`/api/memobase/v1/users/blobs/${user.memobase_id}/chat`);
                    if (blobsResponse.ok) {
                        const blobs = await blobsResponse.json();
                        const blobData = blobs.data || blobs;
                        totalBlobs += Array.isArray(blobData.ids) ? blobData.ids.length : 0;
                    }

                    // 获取profile数量
                    const profileResponse = await fetch(`/api/memobase/v1/users/profile/${user.memobase_id}`);
                    if (profileResponse.ok) {
                        const profile = await profileResponse.json();
                        totalProfiles += Array.isArray(profile) ? profile.length : 0;
                    }
                } catch (e) {
                    console.warn(`获取用户 ${user.user_id} 统计失败:`, e);
                }
            }

            alert(`Memobase统计信息:\n已映射用户数: ${mappedUsers.length}\n总聊天记录数: ${totalBlobs}\n总画像数: ${totalProfiles}`);

        } catch (error) {
            console.error('获取统计信息失败:', error);
            alert(`获取统计信息失败: ${error.message}`);
        }
    }

    async testMemobaseConnection() {
        try {
            const startTime = Date.now();
            const response = await fetch('/api/memobase/v1/healthcheck');
            const endTime = Date.now();
            const latency = endTime - startTime;

            if (response.ok) {
                const result = await response.json();
                alert(`连接测试结果:\n状态: 连接成功\n延迟: ${latency}ms\n响应: ${JSON.stringify(result)}`);
            } else {
                alert(`连接测试失败: HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('连接测试失败:', error);
            alert(`连接测试失败: ${error.message}`);
        }
    }

    async viewMemobaseLogs() {
        // Memobase没有提供日志API，显示替代信息
        alert('Memobase日志查看功能不可用\n\n建议：\n1. 查看Memobase服务器控制台日志\n2. 检查Docker容器日志\n3. 使用连接测试功能检查服务状态');
    }

    async clearMemobaseCache() {
        // Memobase没有提供缓存清除API，显示替代信息
        alert('Memobase缓存清除功能不可用\n\n建议：\n1. 重启Memobase服务\n2. 使用记忆刷新功能重新生成用户画像\n3. 检查Memobase配置文件中的缓存设置');
    }

    // 工具方法
    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN');
    }

    formatMemobaseId(memobaseId) {
        if (!memobaseId || memobaseId === '未映射') {
            return '<span class="status-unmapped">未映射</span>';
        }
        // 显示前8位和后4位，中间用省略号
        if (memobaseId.length > 12) {
            const start = memobaseId.substring(0, 8);
            const end = memobaseId.substring(memobaseId.length - 4);
            return `<span class="memobase-uuid" title="${memobaseId}">${start}...${end}</span>`;
        }
        return `<span class="memobase-uuid" title="${memobaseId}">${memobaseId}</span>`;
    }

    formatTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
        if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
        return Math.floor(diff / 86400000) + '天前';
    }

    viewUserMemories(userId) {
        // 切换到记忆管理页面并选择用户
        this.switchPage('memories');
        setTimeout(() => {
            document.getElementById('memoryUserSelect').value = userId;
            this.loadUserMemories();
        }, 100);
    }
}

// 记忆管理相关方法
AdminManager.prototype.editPersonaMemory = function(memoryId) {
    // 编辑虚拟人记忆功能待实现
    console.log('编辑记忆:', memoryId);
    alert('编辑记忆功能开发中...');
};

AdminManager.prototype.deletePersonaMemory = function(memoryId) {
    if (confirm('确定要删除这条记忆吗？')) {
        // 删除虚拟人记忆功能待实现
        console.log('删除记忆:', memoryId);
        alert('删除记忆功能开发中...');
    }
};

// 记忆管理选项卡切换
function switchMemoryTab(tabName) {
    // 更新选项卡状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[onclick="switchMemoryTab('${tabName}')"]`).classList.add('active');

    // 更新内容显示
    document.querySelectorAll('.memory-tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabName}-memory-tab`).classList.add('active');
}

// 全局函数
function refreshUsers() {
    adminManager.loadUsers();
}

function searchUsers() {
    // 搜索功能待实现
    console.log('搜索用户功能待实现');
}

function loadUserMemories() {
    adminManager.loadUserMemories();
}

function loadPersonaMemories() {
    adminManager.loadPersonaMemories();
}

function refreshMemoryStats() {
    adminManager.refreshMemoryStats();
}

function searchUserMemories() {
    // 搜索用户记忆功能待实现
    console.log('搜索用户记忆功能待实现');
}

function searchPersonaMemories() {
    // 搜索虚拟人记忆功能待实现
    console.log('搜索虚拟人记忆功能待实现');
}

function addPersonaMemory() {
    // 添加虚拟人记忆功能待实现
    console.log('添加虚拟人记忆功能待实现');
    alert('添加记忆功能开发中...');
}

function exportPersonaMemories() {
    // 导出虚拟人记忆功能待实现
    console.log('导出虚拟人记忆功能待实现');
    alert('导出记忆功能开发中...');
}

function loadPersonaConfig() {
    adminManager.loadPersonaConfig();
}

function saveConfig() {
    // 保存配置功能待实现
    console.log('保存配置功能待实现');
}

// Memobase管理全局函数
function refreshMemobaseStatus() {
    adminManager.refreshMemobaseStatus();
}

function flushAllMemories() {
    if (confirm('确定要刷新所有用户的记忆吗？这可能需要一些时间。')) {
        // 刷新所有记忆功能待实现
        console.log('刷新所有记忆功能待实现');
        alert('刷新所有记忆功能开发中...');
    }
}

function loadMemobaseUserDetails() {
    const userId = document.getElementById('memobaseUserSelect').value;
    if (!userId) {
        alert('请先选择用户');
        return;
    }
    adminManager.loadMemobaseUserDetails(userId);
}

function flushUserMemory() {
    const userId = document.getElementById('memobaseUserSelect').value;
    if (!userId) {
        alert('请先选择用户');
        return;
    }
    adminManager.flushUserMemory(userId);
}

function switchMemobaseTab(tabName) {
    // 更新选项卡状态
    document.querySelectorAll('.memobase-tabs .tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[onclick="switchMemobaseTab('${tabName}')"]`).classList.add('active');

    // 更新内容显示
    document.querySelectorAll('.memobase-tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

function loadUserBlobs() {
    const userId = document.getElementById('memobaseUserSelect').value;
    if (!userId) {
        alert('请先选择用户');
        return;
    }
    adminManager.loadUserBlobs(userId);
}

function loadUserProfile() {
    const userId = document.getElementById('memobaseUserSelect').value;
    if (!userId) {
        alert('请先选择用户');
        return;
    }
    adminManager.loadUserProfile(userId);
}

function loadUserContext() {
    const userId = document.getElementById('memobaseUserSelect').value;
    if (!userId) {
        alert('请先选择用户');
        return;
    }
    const tokenSize = document.getElementById('contextTokenSize').value || 1000;
    adminManager.loadUserContext(userId, tokenSize);
}

function flushAllUserMemories() {
    if (confirm('确定要刷新所有用户的记忆吗？这可能需要较长时间。')) {
        adminManager.flushAllUserMemories();
    }
}

function exportMemobaseData() {
    adminManager.exportMemobaseData();
}

function getMemobaseStats() {
    adminManager.getMemobaseStats();
}

function testMemobaseConnection() {
    adminManager.testMemobaseConnection();
}

function viewMemobaseLogs() {
    adminManager.viewMemobaseLogs();
}

function clearMemobaseCache() {
    if (confirm('确定要清除Memobase缓存吗？这可能会影响性能。')) {
        adminManager.clearMemobaseCache();
    }
}

// 初始化
const adminManager = new AdminManager();
