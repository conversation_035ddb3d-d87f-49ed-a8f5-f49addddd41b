<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理 - 沈沐心虚拟人系统</title>
    <link rel="stylesheet" href="/admin/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <span class="logo-icon">🌸</span>
                    <span class="logo-text">沈沐心管理</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#dashboard" class="nav-link active" data-page="dashboard">
                            <span class="nav-icon">📊</span>
                            <span class="nav-text">仪表盘</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#users" class="nav-link" data-page="users">
                            <span class="nav-icon">👥</span>
                            <span class="nav-text">用户管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#memories" class="nav-link" data-page="memories">
                            <span class="nav-icon">🧠</span>
                            <span class="nav-text">记忆管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#persona" class="nav-link" data-page="persona">
                            <span class="nav-icon">💭</span>
                            <span class="nav-text">人设管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#memobase" class="nav-link" data-page="memobase">
                            <span class="nav-icon">🗄️</span>
                            <span class="nav-text">Memobase管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#config" class="nav-link" data-page="config">
                            <span class="nav-icon">⚙️</span>
                            <span class="nav-text">系统配置</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <a href="/" class="back-to-chat">
                    <span class="nav-icon">💬</span>
                    <span class="nav-text">返回聊天</span>
                </a>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="topbar">
                <div class="topbar-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                    <h1 class="page-title" id="pageTitle">仪表盘</h1>
                </div>
                <div class="topbar-right">
                    <div class="user-info">
                        <span class="user-name">管理员</span>
                        <div class="user-avatar">👤</div>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 仪表盘页面 -->
                <div id="dashboard-page" class="page-content active">
                    <div class="dashboard-grid">
                        <div class="stat-card">
                            <div class="stat-icon">👥</div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalUsers">-</div>
                                <div class="stat-label">总用户数</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">💬</div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalConversations">-</div>
                                <div class="stat-label">对话总数</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">🧠</div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalMemories">-</div>
                                <div class="stat-label">记忆总数</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">❤️</div>
                            <div class="stat-info">
                                <div class="stat-number" id="avgAffection">-</div>
                                <div class="stat-label">平均好感度</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-charts">
                        <div class="chart-card">
                            <h3>最近活动</h3>
                            <div class="activity-list" id="recentActivity">
                                <div class="loading">加载中...</div>
                            </div>
                        </div>
                        
                        <div class="chart-card">
                            <h3>系统状态</h3>
                            <div class="system-status" id="systemStatus">
                                <div class="status-item">
                                    <span class="status-label">服务状态</span>
                                    <span class="status-value online">正常</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">数据库</span>
                                    <span class="status-value online">连接正常</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">记忆服务</span>
                                    <span class="status-value online">运行中</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户管理页面 -->
                <div id="users-page" class="page-content">
                    <div class="page-header">
                        <h2>用户管理</h2>
                        <div class="page-actions">
                            <button class="btn btn-primary" onclick="refreshUsers()">刷新</button>
                        </div>
                    </div>
                    
                    <div class="content-card">
                        <div class="search-bar">
                            <input type="text" id="userSearch" placeholder="搜索用户..." class="search-input">
                            <button class="btn btn-secondary" onclick="searchUsers()">搜索</button>
                        </div>
                        
                        <div class="table-container">
                            <table class="data-table" id="usersTable">
                                <thead>
                                    <tr>
                                        <th>用户ID</th>
                                        <th>用户名</th>
                                        <th>昵称</th>
                                        <th>Memobase ID</th>
                                        <th>注册时间</th>
                                        <th>最后活动</th>
                                        <th>好感度</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <tr>
                                        <td colspan="8" class="loading">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 记忆管理页面 -->
                <div id="memories-page" class="page-content">
                    <div class="page-header">
                        <h2>记忆管理</h2>
                        <div class="page-actions">
                            <button class="btn btn-primary" onclick="refreshMemoryStats()">刷新统计</button>
                        </div>
                    </div>

                    <!-- 记忆统计概览 -->
                    <div class="content-card">
                        <h3>记忆统计概览</h3>
                        <div class="memory-stats" id="memoryStats">
                            <div class="stat-item">
                                <span class="stat-label">用户记忆总数</span>
                                <span class="stat-value" id="userMemoryCount">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">虚拟人记忆总数</span>
                                <span class="stat-value" id="personaMemoryCount">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">活跃用户数</span>
                                <span class="stat-value" id="activeUserCount">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- 记忆管理选项卡 -->
                    <div class="content-card">
                        <div class="memory-tabs">
                            <button class="tab-btn active" onclick="switchMemoryTab('user')">用户记忆</button>
                            <button class="tab-btn" onclick="switchMemoryTab('persona')">虚拟人记忆</button>
                        </div>

                        <!-- 用户记忆管理 -->
                        <div id="user-memory-tab" class="memory-tab-content active">
                            <div class="memory-controls">
                                <div class="control-group">
                                    <label for="memoryUserSelect">选择用户：</label>
                                    <select id="memoryUserSelect" class="form-select">
                                        <option value="">选择用户</option>
                                    </select>
                                    <button class="btn btn-primary" onclick="loadUserMemories()">查看记忆</button>
                                </div>
                                <div class="control-group">
                                    <input type="text" id="userMemorySearch" placeholder="搜索用户记忆..." class="search-input">
                                    <button class="btn btn-secondary" onclick="searchUserMemories()">搜索</button>
                                </div>
                            </div>

                            <div class="memory-list" id="userMemoryList">
                                <div class="empty-state">请选择用户查看记忆</div>
                            </div>
                        </div>

                        <!-- 虚拟人记忆管理 -->
                        <div id="persona-memory-tab" class="memory-tab-content">
                            <div class="memory-controls">
                                <div class="control-group">
                                    <label for="personaMemoryCategory">记忆类别：</label>
                                    <select id="personaMemoryCategory" class="form-select">
                                        <option value="">全部类别</option>
                                        <option value="basic_info">基本信息</option>
                                        <option value="preferences">偏好设置</option>
                                        <option value="experiences">经历体验</option>
                                        <option value="relationships">人际关系</option>
                                        <option value="emotions">情感记忆</option>
                                    </select>
                                    <button class="btn btn-primary" onclick="loadPersonaMemories()">查看记忆</button>
                                </div>
                                <div class="control-group">
                                    <input type="text" id="personaMemorySearch" placeholder="搜索虚拟人记忆..." class="search-input">
                                    <button class="btn btn-secondary" onclick="searchPersonaMemories()">搜索</button>
                                </div>
                                <div class="control-group">
                                    <button class="btn btn-success" onclick="addPersonaMemory()">添加记忆</button>
                                    <button class="btn btn-warning" onclick="exportPersonaMemories()">导出记忆</button>
                                </div>
                            </div>

                            <div class="memory-list" id="personaMemoryList">
                                <div class="empty-state">点击"查看记忆"加载虚拟人记忆</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 人设管理页面 -->
                <div id="persona-page" class="page-content">
                    <div class="page-header">
                        <h2>人设管理</h2>
                        <div class="page-actions">
                            <button class="btn btn-primary" onclick="loadPersonaConfig()">刷新配置</button>
                        </div>
                    </div>
                    
                    <div class="content-card">
                        <div class="persona-config" id="personaConfig">
                            <div class="loading">加载中...</div>
                        </div>
                    </div>
                </div>

                <!-- Memobase管理页面 -->
                <div id="memobase-page" class="page-content">
                    <div class="page-header">
                        <h2>Memobase管理</h2>
                        <div class="page-actions">
                            <button class="btn btn-primary" onclick="refreshMemobaseStatus()">刷新状态</button>
                            <button class="btn btn-warning" onclick="flushAllMemories()">刷新所有记忆</button>
                        </div>
                    </div>

                    <!-- Memobase服务状态 -->
                    <div class="content-card">
                        <h3>服务状态</h3>
                        <div class="memobase-status" id="memobaseStatus">
                            <div class="status-grid">
                                <div class="status-item">
                                    <span class="status-label">服务连接</span>
                                    <span class="status-value" id="memobaseConnection">检查中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">API版本</span>
                                    <span class="status-value" id="memobaseVersion">-</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">总用户数</span>
                                    <span class="status-value" id="memobaseTotalUsers">-</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">服务地址</span>
                                    <span class="status-value" id="memobaseUrl">-</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 用户详情管理 -->
                    <div class="content-card">
                        <h3>用户详情管理</h3>
                        <div class="memobase-controls">
                            <div class="control-group">
                                <label for="memobaseUserSelect">选择用户：</label>
                                <select id="memobaseUserSelect" class="form-select">
                                    <option value="">选择用户</option>
                                </select>
                                <button class="btn btn-primary" onclick="loadMemobaseUserDetails()">查看详情</button>
                                <button class="btn btn-secondary" onclick="flushUserMemory()">刷新记忆</button>
                            </div>
                        </div>

                        <!-- 用户详情显示 -->
                        <div class="memobase-user-details" id="memobaseUserDetails">
                            <div class="empty-state">请选择用户查看详情</div>
                        </div>
                    </div>

                    <!-- 原始数据查看 -->
                    <div class="content-card">
                        <h3>原始数据查看</h3>
                        <div class="memobase-tabs">
                            <button class="tab-btn active" onclick="switchMemobaseTab('blobs')">聊天记录(Blobs)</button>
                            <button class="tab-btn" onclick="switchMemobaseTab('profile')">用户画像(Profile)</button>
                            <button class="tab-btn" onclick="switchMemobaseTab('context')">上下文(Context)</button>
                        </div>

                        <!-- Blobs选项卡 -->
                        <div id="blobs-tab" class="memobase-tab-content active">
                            <div class="tab-controls">
                                <button class="btn btn-primary" onclick="loadUserBlobs()">加载聊天记录</button>
                                <span class="tab-info">显示用户的原始聊天记录</span>
                            </div>
                            <div class="data-display" id="blobsData">
                                <div class="empty-state">点击"加载聊天记录"查看数据</div>
                            </div>
                        </div>

                        <!-- Profile选项卡 -->
                        <div id="profile-tab" class="memobase-tab-content">
                            <div class="tab-controls">
                                <button class="btn btn-primary" onclick="loadUserProfile()">加载用户画像</button>
                                <span class="tab-info">显示Memobase生成的用户画像</span>
                            </div>
                            <div class="data-display" id="profileData">
                                <div class="empty-state">点击"加载用户画像"查看数据</div>
                            </div>
                        </div>

                        <!-- Context选项卡 -->
                        <div id="context-tab" class="memobase-tab-content">
                            <div class="tab-controls">
                                <button class="btn btn-primary" onclick="loadUserContext()">加载用户上下文</button>
                                <input type="number" id="contextTokenSize" placeholder="Token大小" value="1000" class="form-input" style="width: 120px;">
                                <span class="tab-info">显示用于对话的用户上下文</span>
                            </div>
                            <div class="data-display" id="contextData">
                                <div class="empty-state">点击"加载用户上下文"查看数据</div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统操作 -->
                    <div class="content-card">
                        <h3>系统操作</h3>
                        <div class="system-operations">
                            <div class="operation-group">
                                <h4>批量操作</h4>
                                <button class="btn btn-warning" onclick="flushAllUserMemories()">刷新所有用户记忆</button>
                                <button class="btn btn-info" onclick="exportMemobaseData()">导出Memobase数据</button>
                                <button class="btn btn-secondary" onclick="getMemobaseStats()">获取统计信息</button>
                            </div>
                            <div class="operation-group">
                                <h4>调试工具</h4>
                                <button class="btn btn-secondary" onclick="testMemobaseConnection()">测试连接</button>
                                <button class="btn btn-secondary" onclick="viewMemobaseLogs()">查看日志</button>
                                <button class="btn btn-danger" onclick="clearMemobaseCache()">清除缓存</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统配置页面 -->
                <div id="config-page" class="page-content">
                    <div class="page-header">
                        <h2>系统配置</h2>
                        <div class="page-actions">
                            <button class="btn btn-success" onclick="saveConfig()">保存配置</button>
                        </div>
                    </div>

                    <div class="content-card">
                        <div class="config-form" id="configForm">
                            <div class="loading">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="/admin/admin.js?v=20250603-2310"></script>
</body>
</html>
