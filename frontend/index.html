<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>沈沐心</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 微信风格的聊天界面 -->
    <div class="wechat-container">
        <!-- 顶部导航栏 -->
        <div class="wechat-header">
            <div class="header-left">
                <button class="back-btn" onclick="showChatList()">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            <div class="header-center">
                <div class="contact-info">
                    <div class="contact-name">沈沐心</div>
                    <div class="contact-status" id="contactStatus">在线</div>
                </div>
            </div>
            <div class="header-right">
                <button class="more-btn" onclick="showMoreOptions()">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="1" fill="currentColor"/>
                        <circle cx="19" cy="12" r="1" fill="currentColor"/>
                        <circle cx="5" cy="12" r="1" fill="currentColor"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 聊天消息区域 -->
        <div class="wechat-messages" id="chatMessages">
            <!-- 系统消息 -->
            <div class="system-message">
                <div class="system-text">以下为历史消息</div>
            </div>

            <!-- 默认欢迎消息 -->
            <div class="message-group">
                <div class="message assistant-message">
                    <div class="message-avatar">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23ff9a9e'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16'%3E🌸%3C/text%3E%3C/svg%3E" alt="沈沐心">
                    </div>
                    <div class="message-content">
                        <div class="message-bubble">
                            <div class="message-text">你好！我是沈沐心，很高兴认识你～</div>
                        </div>
                        <div class="message-time">刚刚</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部输入区域 -->
        <div class="wechat-input">
            <div class="input-toolbar">
                <button class="toolbar-btn voice-btn" id="voiceBtn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                        <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                        <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                        <path d="M12 22V20" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>

                <div class="input-area">
                    <input
                        type="text"
                        id="messageInput"
                        class="message-input"
                        placeholder="想和沈沐心说些什么呢..."
                        maxlength="500"
                    >
                    <!-- 语音录制按钮（替换输入框） -->
                    <button id="voiceRecordBtn" class="voice-record-btn" style="display: none;">
                        <span class="record-text">按住 说话</span>
                    </button>
                </div>

                <button class="toolbar-btn emoji-btn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                        <path d="M8 14S9.5 16 12 16S16 14 16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        <path d="M9 9H9.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        <path d="M15 9H15.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </button>

                <button class="toolbar-btn video-btn" id="videoBtn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M23 7L16 12L23 17V7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <rect x="1" y="5" width="15" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>

                <button class="toolbar-btn add-btn" id="addBtn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                        <path d="M12 8V16" stroke="currentColor" stroke-width="2"/>
                        <path d="M8 12H16" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>

                <button id="sendButton" class="send-btn" disabled>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M22 2L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 状态信息面板（可收起） -->
        <div class="status-panel" id="statusPanel">
            <div class="status-header">
                <span>聊天信息</span>
                <button class="close-panel" onclick="hideStatusPanel()">×</button>
            </div>
            <div class="status-content">
                <div class="status-item">
                    <span class="status-label">好感度</span>
                    <div class="affection-display">
                        <span id="affectionValue">30</span>
                        <div class="affection-bar">
                            <div id="affectionProgress" class="affection-progress"></div>
                        </div>
                    </div>
                </div>
                <div class="status-item">
                    <span class="status-label">当前状态</span>
                    <span id="currentActivity">准备中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">用户</span>
                    <div class="user-info">
                        <span id="userDisplay">游客模式</span>
                        <a href="/login.html" class="login-link">登录/注册</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 错误提示 -->
    <div id="errorToast" class="error-toast" style="display: none;">
        <span id="errorMessage"></span>
        <button onclick="hideError()">×</button>
    </div>

    <!-- 语音录制遮罩 -->
    <div id="voiceRecordOverlay" class="voice-record-overlay" style="display: none;">
        <div class="voice-record-modal">
            <div class="voice-animation">
                <div class="voice-wave"></div>
                <div class="voice-wave"></div>
                <div class="voice-wave"></div>
            </div>
            <div class="voice-text">正在录音...</div>
            <div class="voice-hint">松开发送，上滑取消</div>
            <div class="voice-timer">00:00</div>
        </div>
    </div>

    <!-- 视频通话界面 -->
    <div id="videoCallOverlay" class="video-call-overlay" style="display: none;">
        <div class="video-call-container">
            <!-- 虚拟人视频区域 -->
            <div class="video-main">
                <video id="virtualCharacterVideo" class="virtual-video" autoplay muted loop poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='300' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23f0f0f0'/%3E%3Ctext x='200' y='150' text-anchor='middle' fill='%23999' font-size='16'%3E沈沐心视频通话%3C/text%3E%3C/svg%3E">
                    <source src="/static/videos/default.mp4" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
                <!-- 视频上的字幕显示 -->
                <div class="video-subtitle" id="videoSubtitle"></div>
                <!-- 视频状态指示器 -->
                <div class="video-status" id="videoStatus">
                    <span class="status-dot"></span>
                    <span class="status-text">连接中...</span>
                </div>
            </div>

            <!-- 聊天记录小窗口 -->
            <div class="video-chat-mini" id="videoChatMini">
                <div class="mini-header">
                    <span>聊天记录</span>
                    <button class="mini-toggle" onclick="toggleChatMini()">−</button>
                </div>
                <div class="mini-messages" id="miniMessages"></div>
            </div>

            <!-- 视频控制栏 -->
            <div class="video-controls">
                <button class="video-control-btn" id="muteBtn" title="静音/取消静音">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5" stroke="currentColor" stroke-width="2"/>
                        <path d="M19.07 4.93A10 10 0 0 1 19.07 19.07" stroke="currentColor" stroke-width="2"/>
                        <path d="M15.54 8.46A5 5 0 0 1 15.54 15.54" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>

                <button class="video-control-btn voice-call-btn" id="voiceCallBtn" title="语音通话">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                        <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                    </svg>
                </button>

                <button class="video-control-btn end-call-btn" id="endCallBtn" title="结束通话">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M22 16.92V19a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h2.09a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9a16 16 0 0 0 6 6l.27-.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" stroke="#ff4757" stroke-width="2" fill="#ff4757"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
