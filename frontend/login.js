class LoginManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkExistingSession();
    }

    bindEvents() {
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        document.getElementById('registerForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });
    }

    checkExistingSession() {
        const sessionToken = localStorage.getItem('sessionToken');
        if (sessionToken) {
            // 如果已有会话，直接跳转到聊天页面
            window.location.href = '/';
        }
    }

    async handleLogin() {
        const form = document.getElementById('loginForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        this.setLoading('loginBtn', true);
        this.hideMessage();

        try {
            const response = await fetch('/api/users/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                // 保存会话令牌和用户信息
                localStorage.setItem('sessionToken', result.session_token);
                localStorage.setItem('userInfo', JSON.stringify(result.user));
                localStorage.setItem('userId', result.user.user_id);

                this.showMessage('登录成功！正在跳转...', 'success');
                
                // 跳转到聊天页面
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            } else {
                this.showMessage(result.error || '登录失败', 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
        } finally {
            this.setLoading('loginBtn', false);
        }
    }

    async handleRegister() {
        const form = document.getElementById('registerForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // 移除空值
        Object.keys(data).forEach(key => {
            if (!data[key]) {
                delete data[key];
            }
        });

        this.setLoading('registerBtn', true);
        this.hideMessage();

        try {
            const response = await fetch('/api/users/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('注册成功！请登录', 'success');
                
                // 切换到登录标签页
                setTimeout(() => {
                    switchTab('login');
                    // 预填用户名
                    document.getElementById('loginUsername').value = data.username;
                }, 1000);
            } else {
                this.showMessage(result.error || '注册失败', 'error');
            }
        } catch (error) {
            console.error('Register error:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
        } finally {
            this.setLoading('registerBtn', false);
        }
    }

    setLoading(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (loading) {
            button.disabled = true;
            button.innerHTML = '<span class="loading"></span>处理中...';
        } else {
            button.disabled = false;
            button.innerHTML = buttonId === 'loginBtn' ? '登录' : '注册';
        }
    }

    showMessage(text, type) {
        const messageEl = document.getElementById('message');
        messageEl.textContent = text;
        messageEl.className = `message ${type}`;
        messageEl.style.display = 'block';
        
        // 自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                this.hideMessage();
            }, 3000);
        }
    }

    hideMessage() {
        const messageEl = document.getElementById('message');
        messageEl.style.display = 'none';
    }
}

function switchTab(tabName) {
    // 更新按钮状态
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // 找到被点击的按钮并激活
    const clickedButton = Array.from(document.querySelectorAll('.tab-button')).find(btn => 
        btn.textContent.trim() === (tabName === 'login' ? '登录' : '注册')
    );
    if (clickedButton) {
        clickedButton.classList.add('active');
    }

    // 更新内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');

    // 隐藏消息
    document.getElementById('message').style.display = 'none';
}

// 初始化
new LoginManager();
