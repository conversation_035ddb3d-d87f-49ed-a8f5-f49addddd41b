#!/usr/bin/env python3
"""
测试Token优化功能
验证记忆模块的token消耗优化
"""

import sys
import os
import time

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.memobase_persona_memory_manager import MemobasePersonaMemoryManager
from clients.memobase_client import get_memobase_client


def test_token_optimization():
    """测试token优化功能"""
    print("🧪 测试Token优化功能")
    print("=" * 50)
    
    try:
        # 初始化记忆管理器
        memory_manager = MemobasePersonaMemoryManager()
        
        # 模拟不同长度的对话历史
        short_chats = [
            {"role": "user", "content": "你好"},
            {"role": "assistant", "content": "你好！很高兴见到你"}
        ]
        
        medium_chats = [
            {"role": "user", "content": "你好"},
            {"role": "assistant", "content": "你好！很高兴见到你"},
            {"role": "user", "content": "你今天怎么样？"},
            {"role": "assistant", "content": "我今天很好，谢谢你的关心"},
            {"role": "user", "content": "你在做什么？"},
            {"role": "assistant", "content": "我在画画，这是我的爱好之一"}
        ]
        
        long_chats = [
            {"role": "user", "content": "你好"},
            {"role": "assistant", "content": "你好！很高兴见到你"},
            {"role": "user", "content": "你今天怎么样？"},
            {"role": "assistant", "content": "我今天很好，谢谢你的关心"},
            {"role": "user", "content": "你在做什么？"},
            {"role": "assistant", "content": "我在画画，这是我的爱好之一"},
            {"role": "user", "content": "你喜欢画什么？"},
            {"role": "assistant", "content": "我喜欢画风景和人物，特别是自然风光"},
            {"role": "user", "content": "能给我看看你的作品吗？"},
            {"role": "assistant", "content": "当然可以，我最近画了一幅山水画"},
            {"role": "user", "content": "你学画画多久了？"},
            {"role": "assistant", "content": "我从小就开始学，已经有十多年了"},
            {"role": "user", "content": "你有什么绘画技巧可以分享吗？"},
            {"role": "assistant", "content": "最重要的是多观察，多练习，还要有耐心"}
        ]
        
        print("\n📊 测试不同对话长度的token使用情况：")
        
        # 测试短对话
        print("\n🔸 短对话（2轮）:")
        context_short = memory_manager.get_persona_context(
            max_token_size=500,
            chats=short_chats,
            optimize_tokens=True
        )
        print(f"   上下文长度: {len(context_short)} 字符")
        
        # 测试中等对话
        print("\n🔸 中等对话（6轮）:")
        context_medium = memory_manager.get_persona_context(
            max_token_size=500,
            chats=medium_chats,
            optimize_tokens=True
        )
        print(f"   上下文长度: {len(context_medium)} 字符")
        
        # 测试长对话
        print("\n🔸 长对话（14轮）:")
        context_long = memory_manager.get_persona_context(
            max_token_size=500,
            chats=long_chats,
            optimize_tokens=True
        )
        print(f"   上下文长度: {len(context_long)} 字符")
        
        # 对比优化前后的差异
        print("\n📈 对比优化前后的差异：")
        
        # 不启用优化
        context_no_opt = memory_manager.get_persona_context(
            max_token_size=500,
            chats=long_chats,
            optimize_tokens=False
        )
        
        print(f"   优化前: {len(context_no_opt)} 字符")
        print(f"   优化后: {len(context_long)} 字符")
        print(f"   节省: {len(context_no_opt) - len(context_long)} 字符 ({((len(context_no_opt) - len(context_long)) / len(context_no_opt) * 100):.1f}%)")
        
        # 测试token使用统计
        print("\n📊 获取Token使用统计：")
        token_stats = memory_manager.get_token_usage_stats()
        if token_stats and 'error' not in token_stats:
            for key, value in token_stats.items():
                if key not in ['persona_id', 'persona_name']:
                    print(f"   {key}: {value}")
        else:
            print("   ⚠️ 无法获取token统计信息（可能需要Memobase服务运行）")
        
        print("\n✅ Token优化测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_memobase_connection():
    """测试Memobase连接"""
    print("\n🔗 测试Memobase连接")
    print("-" * 30)
    
    try:
        client = get_memobase_client()
        if client.ping():
            print("✅ Memobase连接正常")
            return True
        else:
            print("❌ Memobase连接失败")
            return False
    except Exception as e:
        print(f"❌ Memobase连接异常: {e}")
        return False


def analyze_token_consumption():
    """分析token消耗的原因"""
    print("\n🔍 分析Token消耗原因")
    print("-" * 30)
    
    try:
        memory_manager = MemobasePersonaMemoryManager()
        
        # 获取不同组件的上下文大小
        components = {
            "基础上下文": memory_manager.get_persona_context(max_token_size=100),
            "中等上下文": memory_manager.get_persona_context(max_token_size=300),
            "大型上下文": memory_manager.get_persona_context(max_token_size=500),
        }
        
        print("📏 不同token限制下的上下文大小：")
        for name, context in components.items():
            char_count = len(context)
            estimated_tokens = char_count // 3
            print(f"   {name}: {char_count} 字符, 约 {estimated_tokens} tokens")
        
        # 分析上下文内容
        sample_context = components["中等上下文"]
        if sample_context:
            print(f"\n📝 上下文内容示例（前200字符）:")
            print(f"   {sample_context[:200]}...")
            
            # 分析内容组成
            lines = sample_context.split('\n')
            print(f"\n📊 上下文结构分析:")
            print(f"   总行数: {len(lines)}")
            print(f"   平均行长: {len(sample_context) // len(lines) if lines else 0} 字符")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")


def main():
    """主函数"""
    print("🤖 记忆模块Token消耗分析和优化测试")
    print("=" * 60)
    
    # 测试连接
    if not test_memobase_connection():
        print("\n⚠️ Memobase连接失败，某些测试可能无法正常进行")
        print("   请确保Memobase服务正在运行")
    
    # 分析token消耗
    analyze_token_consumption()
    
    # 测试优化功能
    test_token_optimization()
    
    print("\n" + "=" * 60)
    print("🎯 优化建议:")
    print("1. 使用本地Memobase可以更好地控制token消耗")
    print("2. 根据对话长度动态调整上下文token限制")
    print("3. 限制优先话题数量以减少不必要的上下文")
    print("4. 定期监控token使用情况")
    print("5. 在长对话中适当减少历史上下文的token分配")


if __name__ == '__main__':
    main()
