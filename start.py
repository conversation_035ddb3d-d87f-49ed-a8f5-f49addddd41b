#!/usr/bin/env python3
"""
虚拟人恋爱陪伴系统启动脚本
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import flask
        import requests
        print("✅ 依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r backend/requirements.txt")
        return False

def check_env_file():
    """检查环境变量文件"""
    env_path = os.path.join("backend", ".env")
    example_path = os.path.join("backend", ".env.example")

    if not os.path.exists(env_path):
        if os.path.exists(example_path):
            print("⚠️  未找到.env文件，正在创建...")
            with open(example_path, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(env_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ 已创建.env文件，请编辑其中的API配置")
        else:
            print("❌ 未找到环境变量配置文件")
            return False
    else:
        print("✅ 环境变量文件检查通过")

    return True

def create_database_dir():
    """创建数据库目录"""
    db_dir = "database"
    if not os.path.exists(db_dir):
        os.makedirs(db_dir)
        print("✅ 已创建数据库目录")
    else:
        print("✅ 数据库目录已存在")

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r",
            os.path.join("backend", "requirements.txt")
        ])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def start_server():
    """启动服务器"""
    print("\n🚀 启动虚拟人恋爱陪伴系统...")
    print("=" * 50)

    # 切换到backend目录
    os.chdir("backend")

    try:
        # 启动Flask应用
        subprocess.run([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("🌸 虚拟人恋爱陪伴系统 - 启动检查")
    print("=" * 50)

    # 检查Python版本
    if not check_python_version():
        return

    # 检查依赖
    if not check_dependencies():
        print("\n是否自动安装依赖? (y/n): ", end="")
        if input().lower() in ['y', 'yes', '是']:
            if not install_dependencies():
                return
        else:
            return

    # 检查环境变量文件
    if not check_env_file():
        return

    # 创建数据库目录
    create_database_dir()

    print("\n✅ 所有检查通过!")
    print("\n📝 使用说明:")
    print("1. 确保已在 backend/.env 文件中配置火山引擎API")
    print("2. 服务启动后访问: http://localhost:8080")
    print("3. 按 Ctrl+C 停止服务")

    print("\n按回车键启动服务...")
    input()

    start_server()

if __name__ == "__main__":
    main()
