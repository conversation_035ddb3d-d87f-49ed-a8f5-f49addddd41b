"""
提示词管理模块
使用Jinja2模板引擎管理和渲染提示词
"""

import os
from typing import Dict, List, Any, Optional
from jinja2 import Environment, FileSystemLoader, Template
from datetime import datetime


class PromptManager:
    """提示词管理器"""

    def __init__(self):
        """初始化提示词管理器"""
        self.templates_dir = os.path.join(os.path.dirname(__file__), '..', 'templates', 'prompts')
        self._ensure_templates_dir()

        # 初始化Jinja2环境
        self.env = Environment(
            loader=FileSystemLoader(self.templates_dir),
            trim_blocks=True,
            lstrip_blocks=True,
            autoescape=False
        )

        # 注册自定义过滤器
        self._register_filters()

        # 确保模板文件存在
        self._ensure_template_files()

    def _ensure_templates_dir(self):
        """确保模板目录存在"""
        os.makedirs(self.templates_dir, exist_ok=True)

    def _register_filters(self):
        """注册自定义Jinja2过滤器"""

        def format_time(dt):
            """格式化时间"""
            if isinstance(dt, str):
                return dt
            return dt.strftime('%Y年%m月%d日 %H:%M')

        def emotion_text(emotion):
            """情感文本转换"""
            emotion_map = {
                'happy': '开心',
                'sad': '难过',
                'excited': '兴奋',
                'nostalgic': '怀念',
                'proud': '自豪',
                'embarrassed': '尴尬',
                'neutral': '平静'
            }
            return emotion_map.get(emotion, emotion)

        def affection_level_text(level):
            """好感度等级文本"""
            if level >= 80:
                return "非常亲密"
            elif level >= 60:
                return "比较亲近"
            elif level >= 40:
                return "有些好感"
            elif level >= 20:
                return "初步了解"
            else:
                return "刚刚认识"

        # 注册过滤器
        self.env.filters['format_time'] = format_time
        self.env.filters['emotion_text'] = emotion_text
        self.env.filters['affection_level_text'] = affection_level_text

    def _ensure_template_files(self):
        """确保模板文件存在 - 只在文件不存在时创建默认模板"""
        # 检查模板目录是否有文件，如果没有则创建默认模板
        if not os.listdir(self.templates_dir):
            raise FileNotFoundError(f"模板目录为空，请先创建默认模板: {self.templates_dir}")

    def render_system_prompt(self, **kwargs) -> str:
        """
        渲染完整的对话提示词

        Args:
            **kwargs: 模板参数

        Returns:
            渲染后的对话提示词
        """
        template = self.env.get_template('conversation_prompt.j2')
        return template.render(**kwargs)

    def render_template(self, template_name: str, **kwargs) -> str:
        """
        渲染指定模板

        Args:
            template_name: 模板名称
            **kwargs: 模板参数

        Returns:
            渲染后的文本
        """
        template = self.env.get_template(template_name)
        return template.render(**kwargs)

    def get_template_variables(self, template_name: str) -> List[str]:
        """获取模板中的变量列表"""
        template = self.env.get_template(template_name)
        return list(template.environment.parse(template.source).find_all(
            lambda node: hasattr(node, 'name')
        ))



class PromptTemplateManager:
    """提示词模板管理器 - 用于管理和编辑模板文件"""

    def __init__(self, prompt_manager: PromptManager):
        self.prompt_manager = prompt_manager
        self.templates_dir = prompt_manager.templates_dir

    def list_templates(self) -> List[str]:
        """列出所有模板文件"""
        templates = []
        for filename in os.listdir(self.templates_dir):
            if filename.endswith('.j2'):
                templates.append(filename)
        return sorted(templates)

    def get_template_content(self, template_name: str) -> str:
        """获取模板文件内容"""
        filepath = os.path.join(self.templates_dir, template_name)
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        return ""

    def save_template(self, template_name: str, content: str):
        """保存模板文件"""
        filepath = os.path.join(self.templates_dir, template_name)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

    def create_template(self, template_name: str, content: str):
        """创建新模板"""
        if not template_name.endswith('.j2'):
            template_name += '.j2'
        self.save_template(template_name, content)

    def delete_template(self, template_name: str):
        """删除模板文件"""
        filepath = os.path.join(self.templates_dir, template_name)
        if os.path.exists(filepath):
            os.remove(filepath)

    def validate_template(self, content: str) -> tuple[bool, str]:
        """验证模板语法"""
        try:
            Template(content)
            return True, "模板语法正确"
        except Exception as e:
            return False, f"模板语法错误: {str(e)}"
