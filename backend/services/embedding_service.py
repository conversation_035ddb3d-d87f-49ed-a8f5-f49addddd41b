"""
Embedding服务
使用火山引擎的embedding模型进行文本向量化
"""

import logging
from typing import List, Optional
import numpy as np

try:
    from clients.volcengine_client import get_volcengine_client
    VOLCENGINE_AVAILABLE = True
except ImportError:
    VOLCENGINE_AVAILABLE = False
from config import Config

logger = logging.getLogger(__name__)


class EmbeddingService:
    """Embedding服务"""
    
    def __init__(self):
        if VOLCENGINE_AVAILABLE:
            self.client = get_volcengine_client()
            self.model = Config.VOLCENGINE_CONFIG.get('embedding_model', 'doubao-embedding-text-240715')
            self.embedding_dim = Config.VOLCENGINE_CONFIG.get('embedding_dim', 2560)
        else:
            self.client = None
            self.model = 'mock-embedding'
            self.embedding_dim = 2560  # 与数据库保持一致

        self.max_batch_size = 16  # 批处理大小
        logger.info(f"✅ Embedding服务初始化成功，模型: {self.model}, 维度: {self.embedding_dim}")
    
    def get_embedding(self, text: str) -> Optional[List[float]]:
        """获取单个文本的embedding"""
        try:
            embeddings = self.get_embeddings([text])
            return embeddings[0] if embeddings else None
        except Exception as e:
            logger.error(f"💥 获取embedding失败: {e}")
            return None
    
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """批量获取文本的embedding"""
        try:
            if not texts:
                return []
            
            # 分批处理
            all_embeddings = []
            for i in range(0, len(texts), self.max_batch_size):
                batch = texts[i:i + self.max_batch_size]
                batch_embeddings = self._get_batch_embeddings(batch)
                all_embeddings.extend(batch_embeddings)
            
            logger.info(f"✅ 获取embedding成功: {len(texts)} 个文本")
            return all_embeddings
            
        except Exception as e:
            logger.error(f"💥 批量获取embedding失败: {e}")
            return []
    
    def _get_batch_embeddings(self, texts: List[str]) -> List[List[float]]:
        """获取一批文本的embedding"""
        try:
            if VOLCENGINE_AVAILABLE and self.client:
                # 使用火山引擎的embedding API
                response = self.client.embeddings(
                    input_texts=texts,
                    model=self.model
                )

                if response.get('success'):
                    embeddings = []
                    for data in response['data']:
                        embeddings.append(data['embedding'])

                    logger.info(f"✅ 火山引擎embedding调用成功: {len(embeddings)} 个向量")
                    return embeddings
                else:
                    logger.error(f"❌ 火山引擎embedding调用失败: {response.get('error', 'Unknown error')}")
                    raise Exception(f"火山引擎API错误: {response.get('error')}")
            else:
                logger.warning("⚠️ 火山引擎不可用，使用mock embedding")
                # 使用mock embedding（用于测试）
                import hashlib
                embeddings = []
                for text in texts:
                    # 基于文本内容生成确定性的mock embedding
                    hash_obj = hashlib.md5(text.encode())
                    hash_bytes = hash_obj.digest()

                    # 将hash转换为指定维度的向量
                    embedding = []
                    for i in range(self.embedding_dim):
                        byte_idx = i % len(hash_bytes)
                        embedding.append((hash_bytes[byte_idx] / 255.0 - 0.5) * 2)  # 归一化到[-1, 1]

                    embeddings.append(embedding)

                return embeddings

        except Exception as e:
            logger.error(f"💥 调用embedding API失败: {e}")
            # 返回随机向量作为fallback
            logger.warning(f"⚠️ 使用随机向量作为fallback")
            import random
            return [[random.uniform(-1, 1) for _ in range(self.embedding_dim)] for _ in texts]
    
    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """计算两个embedding的余弦相似度"""
        try:
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # 计算余弦相似度
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"💥 计算相似度失败: {e}")
            return 0.0
    
    def find_most_similar(self, query_embedding: List[float], 
                         candidate_embeddings: List[List[float]], 
                         threshold: float = 0.7) -> List[tuple]:
        """找到最相似的embedding"""
        try:
            similarities = []
            
            for i, candidate in enumerate(candidate_embeddings):
                similarity = self.calculate_similarity(query_embedding, candidate)
                if similarity >= threshold:
                    similarities.append((i, similarity))
            
            # 按相似度降序排序
            similarities.sort(key=lambda x: x[1], reverse=True)
            return similarities
            
        except Exception as e:
            logger.error(f"💥 查找相似embedding失败: {e}")
            return []


# 全局服务实例
embedding_service = None

def get_embedding_service() -> EmbeddingService:
    """获取embedding服务实例"""
    global embedding_service
    if embedding_service is None:
        embedding_service = EmbeddingService()
    return embedding_service
