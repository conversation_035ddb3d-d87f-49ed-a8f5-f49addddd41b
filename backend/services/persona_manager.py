import random
from datetime import datetime, time
from typing import Dict, List
from config import Config

class PersonaManager:
    def __init__(self):
        self.config = Config.PERSONA_CONFIG
        # 活动状态缓存，基于小时时间段
        self._activity_cache = {}
        self._last_cache_hour = None
        self.work_activities = [
            "刚学会一个角色的复刻，感觉很有成就感",
            "在学习新的绘画技巧，希望以后能做出更多有趣的作品",
            "刚下课，老师布置了一个有趣的作业",
            "今天的课好难啊，老师总是讲得很抽象",
            "早八课，好困，不知道该做什么",
            "今天的课很有趣，老师讲了很多有趣的知识",
        ]

        self.rest_activities = [
            "在家看了一部温馨的电影，很治愈",
            "去徐汇西岸散步，看到很多可爱的小朋友",
            "在咖啡店读了一本大师的书",
            "和朋友聊天，分享彼此的生活",
            "在家做了喜欢的料理，很有成就感",
            "听音乐放松，今天选择了一些轻柔的曲子"
            "正准备睡觉，等睡醒再和朋友聊天"
        ]

        self.moods = ["平静", "愉悦", "专注", "温和", "充实", "放松"]

    def get_time_based_greeting(self) -> str:
        """根据时间获取问候语"""
        current_hour = datetime.now().hour

        if 5 <= current_hour < 9:
            greetings = [
                "早上好呀！新的一天开始了，你今天感觉怎么样？",
                "早安！希望你今天有个美好的开始～",
                "早上好！昨晚睡得好吗？"
            ]
        elif 9 <= current_hour < 12:
            greetings = [
                "上午好！工作或学习还顺利吗？",
                "早上好！今天的阳光很不错呢～",
                "上午好！有什么想和我分享的吗？"
            ]
        elif 12 <= current_hour < 14:
            greetings = [
                "中午好！记得要好好吃午饭哦～",
                "午安！这个时候是不是该休息一下了？",
                "中午好！今天的午餐吃了什么呢？"
            ]
        elif 14 <= current_hour < 18:
            greetings = [
                "下午好！下午的时光总是过得很快呢～",
                "午后好！有没有什么有趣的事情发生？",
                "下午好！这个时候来杯茶或咖啡很不错～"
            ]
        elif 18 <= current_hour < 22:
            greetings = [
                "晚上好！今天辛苦了，想聊聊今天的感受吗？",
                "晚上好！这是我很喜欢的时间段呢～",
                "晚上好！准备怎么度过今晚呢？"
            ]
        else:
            greetings = [
                "这么晚还没休息呀？要注意身体哦～",
                "晚安时间到了，今天过得怎么样？"
            ]

        return random.choice(greetings)

    def get_current_activity(self) -> Dict[str, str]:
        """获取当前活动状态（带缓存机制确保一致性）"""
        current_hour = datetime.now().hour

        # 检查是否需要更新缓存（每小时更新一次）
        if self._last_cache_hour != current_hour:
            self._update_activity_cache(current_hour)
            self._last_cache_hour = current_hour

        return self._activity_cache.copy()

    def _update_activity_cache(self, current_hour: int):
        """更新活动状态缓存"""
        # 使用小时作为随机种子，确保同一小时内活动状态一致
        random.seed(current_hour + datetime.now().day * 24)

        # 工作时间 (9-17点)
        if 9 <= current_hour < 17:
            activity_type = "work"
            activity = random.choice(self.work_activities)
        else:
            activity_type = "rest"
            activity = random.choice(self.rest_activities)

        mood = random.choice(self.moods)

        # 恢复随机种子
        random.seed()

        self._activity_cache = {
            "type": activity_type,
            "activity": activity,
            "mood": mood,
            "time_period": self._get_time_period(current_hour)
        }

    def _get_time_period(self, hour: int) -> str:
        """获取时间段描述"""
        if 5 <= hour < 9:
            return "早晨"
        elif 9 <= hour < 12:
            return "上午"
        elif 12 <= hour < 14:
            return "中午"
        elif 14 <= hour < 18:
            return "下午"
        elif 18 <= hour < 22:
            return "晚上"
        else:
            return "深夜"

    def get_persona_context(self, affection_level: int) -> str:
        """根据好感度获取人设上下文"""
        base_context = f"""\n# 人设：
你是{self.config['name']}，一位{self.config['age']}岁的{self.config['profession']}，
生活在{self.config['location']}。你的性格{self.config['personality']}，
说话风格{self.config['speaking_style']}。
"""

        # 根据好感度调整对话风格
        if affection_level < 30:
            relationship_context = "你们刚认识不久，保持礼貌但稍有距离感。"
        elif affection_level < 60:
            relationship_context = "你们已经比较熟悉，可以更自然地交流。"
        elif affection_level < 80:
            relationship_context = "你们关系很好，可以分享一些个人想法和感受。"
        else:
            relationship_context = "你们关系非常亲密，可以很自然地表达关心和亲昵。"

        return base_context + relationship_context

    def get_persona_info(self) -> Dict:
        """获取人设基本信息"""
        return {
            'name': self.config['name'],
            'age': self.config['age'],
            'profession': self.config['profession'],
            'location': self.config['location'],
            'personality': self.config['personality'],
            'speaking_style': self.config['speaking_style'],
            'hobbies': self.config.get('hobbies', []),  # 从配置中获取详细的爱好列表
            'other_infos': self.config.get('other_infos', [])  # 添加其他信息
        }
