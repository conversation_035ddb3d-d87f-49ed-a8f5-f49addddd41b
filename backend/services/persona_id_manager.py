"""
虚拟人ID管理器
负责生成和管理项目唯一的虚拟人ID
"""

import os
import json
import logging
from datetime import datetime
from config import Config

logger = logging.getLogger(__name__)


class PersonaIdManager:
    """虚拟人ID管理器"""
    
    def __init__(self):
        """初始化虚拟人ID管理器"""
        self.config = Config.PERSONA_CONFIG
        self.id_file_path = os.path.join(
            os.path.dirname(__file__),
            '..',
            'database',
            'persona_id.json'
        )

        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.id_file_path), exist_ok=True)

        # 尝试从文件加载现有ID，如果没有则使用默认固定ID
        self._persona_id = self._load_persona_id_from_file()

        logger.info(f"✅ 虚拟人ID管理器初始化完成 - ID: {self._persona_id}")
    
    def get_persona_id(self) -> str:
        """
        获取虚拟人ID
        
        Returns:
            虚拟人的唯一ID
        """
        return self._persona_id

    def _load_persona_id_from_file(self) -> str:
        """
        从文件加载虚拟人ID，如果文件不存在则返回默认固定ID

        Returns:
            虚拟人ID
        """
        try:
            if os.path.exists(self.id_file_path):
                with open(self.id_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                if isinstance(data, dict) and 'persona_id' in data:
                    stored_id = data['persona_id']
                    logger.info(f"📋 从文件加载虚拟人ID: {stored_id}")
                    return stored_id

            # 文件不存在或格式错误，返回默认固定ID
            default_id = "12345678-1234-5678-9abc-123456789012"
            logger.info(f"🆕 使用默认虚拟人ID: {default_id}")
            return default_id

        except Exception as e:
            logger.error(f"💥 加载虚拟人ID文件失败: {e}")
            # 出错时返回默认固定ID
            default_id = "12345678-1234-5678-9abc-123456789012"
            logger.info(f"🆕 使用默认虚拟人ID: {default_id}")
            return default_id

    def save_persona_id(self, persona_id: str):
        """
        保存虚拟人ID到文件

        Args:
            persona_id: 要保存的虚拟人ID
        """
        try:
            data = {
                'persona_id': persona_id,
                'created_at': datetime.now().isoformat(),
                'persona_name': self.config.get('name', '沈沐心'),
                'persona_nickname': self.config.get('nickname', '沈沐心'),
                'status': 'memobase_generated',
                'description': '由Memobase服务生成的UUID'
            }

            with open(self.id_file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # 更新内存中的ID
            self._persona_id = persona_id

            logger.info(f"💾 虚拟人ID已保存: {persona_id}")

        except Exception as e:
            logger.error(f"💥 保存虚拟人ID失败: {e}")

    def regenerate_persona_id(self) -> str:
        """
        重新生成虚拟人ID

        简化版本：返回固定ID，不需要重新生成

        Returns:
            固定的虚拟人ID
        """
        logger.info("🎯 使用固定虚拟人ID，无需重新生成")
        return "12345678-1234-5678-9abc-123456789012"
    
    def get_id_info(self) -> dict:
        """
        获取虚拟人ID的详细信息

        简化版本：返回固定ID的信息

        Returns:
            包含固定ID详细信息的字典
        """
        return {
            'persona_id': "12345678-1234-5678-9abc-123456789012",
            'created_at': "2025-06-02T00:00:00",
            'persona_name': self.config.get('name', '沈沐心'),
            'persona_nickname': self.config.get('nickname', '沈沐心'),
            'status': 'fixed_uuid',
            'description': '固定UUID，专用于虚拟人恋爱陪伴系统'
        }


# 全局单例实例
_persona_id_manager = None


def get_persona_id_manager() -> PersonaIdManager:
    """
    获取虚拟人ID管理器单例实例
    
    Returns:
        虚拟人ID管理器实例
    """
    global _persona_id_manager
    if _persona_id_manager is None:
        _persona_id_manager = PersonaIdManager()
    return _persona_id_manager


def get_persona_id() -> str:
    """
    获取虚拟人ID的便捷函数
    
    Returns:
        虚拟人ID
    """
    return get_persona_id_manager().get_persona_id()
