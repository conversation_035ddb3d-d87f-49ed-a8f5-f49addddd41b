"""
聊天历史记录服务
通过PostgreSQL获取聊天历史记录
"""

from typing import List, Dict, Optional
from models.memory import Conversation, User, get_db_connection, _get_user_uuid, PROJECT_ID


class ChatHistoryService:
    """聊天历史记录服务"""

    def __init__(self):
        pass
    
    def get_chat_history_with_pagination(self, user_id: str, limit: int = 20,
                                       offset: int = 0, before_timestamp: str = None) -> List[Dict]:
        """获取分页的聊天历史记录"""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()

                # 获取用户的UUID
                user_uuid = _get_user_uuid(cursor, user_id)
                if not user_uuid:
                    return []

                # 构建查询条件
                where_clause = "WHERE user_id = %s AND project_id = %s"
                params = [user_uuid, PROJECT_ID]

                if before_timestamp:
                    where_clause += " AND timestamp < %s"
                    params.append(before_timestamp)

                # 查询对话记录
                query = f"""
                    SELECT message, sender, timestamp, emotion_score
                    FROM conversations
                    {where_clause}
                    ORDER BY timestamp DESC
                    LIMIT %s OFFSET %s
                """
                params.extend([limit, offset])

                cursor.execute(query, params)
                conversations = cursor.fetchall()

                return [
                    {
                        'message': conv[0],
                        'sender': conv[1],
                        'timestamp': conv[2].isoformat() if hasattr(conv[2], 'isoformat') else str(conv[2]),
                        'emotion_score': conv[3] if conv[3] is not None else 0.0
                    }
                    for conv in reversed(conversations)  # 按时间正序返回
                ]

        except Exception as e:
            print(f"获取聊天历史失败: {e}")
            return []
    
    def get_recent_conversations(self, user_id: str, limit: int = 10) -> List[Dict]:
        """获取最近对话记录"""
        return Conversation.get_recent(user_id, limit)

    def save_conversation(self, user_id: str, message: str, sender: str, emotion_score: float = 0.0):
        """保存对话记录"""
        conversation = Conversation(user_id, message, sender, emotion_score)
        conversation.save()


# 全局实例
chat_history_service = None

def get_chat_history_service() -> ChatHistoryService:
    """获取聊天历史服务实例"""
    global chat_history_service
    if chat_history_service is None:
        chat_history_service = ChatHistoryService()
    return chat_history_service
