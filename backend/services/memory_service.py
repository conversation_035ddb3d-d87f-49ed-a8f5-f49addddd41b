"""
简化的记忆服务
直接操作PostgreSQL数据库，处理用户记忆的提取、存储和检索
"""

import logging
from typing import Dict, List
from datetime import datetime

from models.memory import User, UserEvent
from services.embedding_service import get_embedding_service
from services.memory_analyzer import get_memory_analyzer
from config import Config

logger = logging.getLogger(__name__)


class MemoryService:
    """简化的记忆服务 - 直接操作数据库"""

    def __init__(self):
        self.config = Config.MEMORY_CONFIG
        self.embedding_service = get_embedding_service()
        self.analyzer = get_memory_analyzer()
        logger.info("✅ 增强记忆服务初始化成功")
    
    def extract_memories_from_text(self, user_id: str, text: str) -> List[Dict]:
        """从文本中提取记忆信息（简化版本）"""
        try:
            # 确保用户存在
            User.create(user_id)

            # 使用LLM分析文本
            analysis = self.analyzer.analyze_text(text, user_id)

            # 获取文本的embedding
            text_embedding = self.embedding_service.get_embedding(text)

            # 创建用户事件
            event_data = {
                'profile_delta': analysis.get('profile_deltas', []),
                'event_tip': analysis.get('event_tip', ''),
                'event_tags': analysis.get('event_tags', []),
                'original_text': text
            }

            event = UserEvent(user_id, event_data, text_embedding)
            event_id = event.save()

            logger.info(f"✅ 记忆提取完成 - 用户: {user_id}, 事件: {event_id}")

            return [{
                'event_id': event_id,
                'profile_ids': [],  # 简化版本暂不处理画像
                'type': 'analyzed',
                'content': text[:100] + '...' if len(text) > 100 else text,
                'analysis': analysis,
                'timestamp': datetime.now().isoformat()
            }]

        except Exception as e:
            logger.error(f"💥 记忆提取失败: {e}")
            return []
    
    def get_relevant_memories(self, user_id: str, query_text: str = None,
                            limit: int = 5) -> List[Dict]:
        """获取相关记忆（简化版本）"""
        try:
            # 简化版本：返回用户事件作为记忆
            user_events = UserEvent.get_user_events(user_id, limit)
            memories = []

            for event in user_events:
                event_data = event.get('event_data', {})
                memories.append({
                    'content': event_data.get('original_text', ''),
                    'type': 'event',
                    'sub_type': '',
                    'importance': 1.0,
                    'similarity': 1.0,
                    'created_at': event['created_at'].isoformat() if event.get('created_at') else '',
                    'source': 'user_event'
                })

            logger.info(f"🔍 获取相关记忆 - 用户: {user_id}, 结果: {len(memories)} 条")
            return memories[:limit]

        except Exception as e:
            logger.error(f"💥 获取相关记忆失败: {e}")
            return []
    
    def get_user_context(self, user_id: str, max_token_size: int = 500,
                        prefer_topics: List[str] = None, query_text: str = None) -> str:
        """获取用户上下文（简化版本）"""
        try:
            # 简化版本：返回基本的用户信息
            memories = self.get_relevant_memories(user_id, query_text, limit=3)
            context_parts = []

            for memory in memories:
                content = memory.get('content', '')
                if content:
                    context_parts.append(f"[记忆] {content[:100]}")

            context = "\n".join(context_parts)
            logger.info(f"📄 生成用户上下文: {len(context)} 字符")

            return context

        except Exception as e:
            logger.error(f"💥 获取用户上下文失败: {e}")
            return ""
    


    def init_persona_memories(self, persona_id: str = None) -> bool:
        """初始化虚拟人记忆（简化版本）"""
        try:
            persona_id = persona_id or Config.PERSONA_CONFIG['persona_id_prefix'] + '_persona'

            # 创建虚拟人用户
            User.create(persona_id, Config.PERSONA_CONFIG['name'])

            logger.info(f"🤖 虚拟人 {persona_id} 初始化完成")
            return True

        except Exception as e:
            logger.error(f"💥 初始化虚拟人记忆失败: {e}")
            return False

    def get_persona_context(self, persona_id: str = None, max_token_size: int = 300) -> str:
        """获取虚拟人上下文（简化版本）"""
        try:
            # 返回基本的虚拟人信息
            persona_info = Config.PERSONA_CONFIG
            context = f"我是{persona_info['name']}，{persona_info['age']}岁，{persona_info['profession']}"

            logger.info(f"🤖 生成虚拟人上下文: {len(context)} 字符")
            return context

        except Exception as e:
            logger.error(f"💥 获取虚拟人上下文失败: {e}")
            return ""

    def get_memory_stats(self) -> Dict:
        """获取记忆统计（简化版本）"""
        try:
            return {
                'users': 0,
                'memories': 0,
                'profiles': 0,
                'conversations': 0,
                'provider': 'simplified_postgresql'
            }

        except Exception as e:
            logger.error(f"💥 获取统计失败: {e}")
            return {'error': str(e)}


# 全局服务实例
memory_service = None

def get_memory_service() -> MemoryService:
    """获取记忆服务实例"""
    global memory_service
    if memory_service is None:
        memory_service = MemoryService()
    return memory_service
