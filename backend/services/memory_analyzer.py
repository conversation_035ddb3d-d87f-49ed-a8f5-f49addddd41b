"""
记忆分析服务
使用LLM分析文本，提取topic、tag和profile信息
"""

import logging
import json
from typing import Dict, List, Optional

try:
    from services.llm_service import llm_service
    LLM_AVAILABLE = True
except ImportError:
    LLM_AVAILABLE = False
from config import Config

logger = logging.getLogger(__name__)


class MemoryAnalyzer:
    """记忆分析器"""
    
    def __init__(self):
        if LLM_AVAILABLE:
            self.llm = llm_service
        else:
            self.llm = None

        self.topics = self._load_topics()
        self.tags = self._load_tags()
        logger.info("✅ 记忆分析器初始化成功")
    
    def _load_topics(self) -> List[str]:
        """加载预定义的topic列表"""
        return [
            "basic_info",      # 基本信息
            "personality",     # 性格特点
            "interests",       # 兴趣爱好
            "experience",      # 经历体验
            "emotion",         # 情感状态
            "relationship",    # 人际关系
            "goal",           # 目标计划
            "preference",     # 偏好选择
            "skill",          # 技能能力
            "lifestyle"       # 生活方式
        ]
    
    def _load_tags(self) -> List[str]:
        """加载预定义的tag列表"""
        return [
            "positive",       # 积极的
            "negative",       # 消极的
            "important",      # 重要的
            "casual",         # 随意的
            "private",        # 私密的
            "public",         # 公开的
            "recent",         # 最近的
            "past",           # 过去的
            "future",         # 未来的
            "factual",        # 事实性的
            "emotional",      # 情感性的
            "opinion"         # 观点性的
        ]
    
    def analyze_text(self, text: str, user_id: str = None) -> Dict:
        """分析文本，提取记忆信息"""
        try:
            if LLM_AVAILABLE and self.llm:
                # 构建分析提示词
                prompt = self._build_analysis_prompt(text)

                # 调用LLM分析
                response = self.llm.generate_response(prompt, max_tokens=500)

                # 解析LLM响应
                analysis = self._parse_llm_response(response)

                logger.info(f"✅ 文本分析完成: {len(analysis.get('profile_deltas', []))} 个画像")
                return analysis
            else:
                # 使用fallback分析
                logger.info("⚠️ LLM不可用，使用fallback分析")
                return self._fallback_analysis(text)

        except Exception as e:
            logger.error(f"💥 文本分析失败: {e}")
            return self._fallback_analysis(text)
    
    def _build_analysis_prompt(self, text: str) -> str:
        """构建分析提示词"""
        topics_str = ", ".join(self.topics)
        tags_str = ", ".join(self.tags)
        
        prompt = f"""
请分析以下用户文本，提取用户画像信息。

用户文本：
{text}

请按照以下JSON格式返回分析结果：
{{
    "profile_deltas": [
        {{
            "content": "提取的画像内容",
            "attributes": {{
                "topic": "从以下选择: {topics_str}",
                "sub_topic": "具体的子话题",
                "confidence": 0.8
            }}
        }}
    ],
    "event_tip": "对这次对话的简要总结",
    "event_tags": [
        {{
            "tag": "从以下选择: {tags_str}",
            "value": "标签的具体值"
        }}
    ]
}}

要求：
1. 只提取明确的、有价值的信息
2. confidence范围0.1-1.0，表示提取的置信度
3. 每个profile_delta应该是独立的信息点
4. 如果没有明确信息，返回空数组
5. 必须返回有效的JSON格式
"""
        return prompt
    
    def _parse_llm_response(self, response: str) -> Dict:
        """解析LLM响应"""
        try:
            # 尝试直接解析JSON
            analysis = json.loads(response.strip())
            
            # 验证格式
            if not isinstance(analysis, dict):
                raise ValueError("响应不是字典格式")
            
            # 确保必要字段存在
            analysis.setdefault('profile_deltas', [])
            analysis.setdefault('event_tip', '')
            analysis.setdefault('event_tags', [])
            
            # 验证profile_deltas格式
            valid_deltas = []
            for delta in analysis.get('profile_deltas', []):
                if isinstance(delta, dict) and 'content' in delta:
                    # 确保attributes存在
                    if 'attributes' not in delta:
                        delta['attributes'] = {}
                    
                    # 验证topic
                    topic = delta['attributes'].get('topic', 'basic_info')
                    if topic not in self.topics:
                        delta['attributes']['topic'] = 'basic_info'
                    
                    # 设置默认confidence
                    if 'confidence' not in delta['attributes']:
                        delta['attributes']['confidence'] = 0.7
                    
                    valid_deltas.append(delta)
            
            analysis['profile_deltas'] = valid_deltas
            
            return analysis
            
        except Exception as e:
            logger.error(f"💥 解析LLM响应失败: {e}")
            return self._fallback_analysis(response)
    
    def _fallback_analysis(self, text: str) -> Dict:
        """备用分析方法（基于规则）"""
        try:
            profile_deltas = []
            event_tags = []
            
            # 简单的关键词匹配
            keywords_map = {
                'basic_info': ['我是', '我叫', '我的名字', '我今年', '岁', '我住在', '我来自'],
                'interests': ['喜欢', '爱好', '兴趣', '热爱', '痴迷'],
                'emotion': ['开心', '难过', '高兴', '郁闷', '兴奋', '紧张', '焦虑'],
                'experience': ['去过', '做过', '经历', '体验', '参加'],
                'personality': ['性格', '脾气', '个性', '特点']
            }
            
            for topic, keywords in keywords_map.items():
                for keyword in keywords:
                    if keyword in text:
                        # 提取包含关键词的句子
                        sentences = text.split('。')
                        for sentence in sentences:
                            if keyword in sentence and len(sentence.strip()) > 3:
                                profile_deltas.append({
                                    'content': sentence.strip(),
                                    'attributes': {
                                        'topic': topic,
                                        'sub_topic': 'extracted',
                                        'confidence': 0.6
                                    }
                                })
                                break
                        break
            
            # 添加情感标签
            if any(word in text for word in ['开心', '高兴', '兴奋']):
                event_tags.append({'tag': 'positive', 'value': 'happy'})
            elif any(word in text for word in ['难过', '郁闷', '焦虑']):
                event_tags.append({'tag': 'negative', 'value': 'sad'})
            
            return {
                'profile_deltas': profile_deltas[:3],  # 限制数量
                'event_tip': f"用户分享了关于{len(profile_deltas)}个方面的信息",
                'event_tags': event_tags
            }
            
        except Exception as e:
            logger.error(f"💥 备用分析失败: {e}")
            return {
                'profile_deltas': [],
                'event_tip': '对话记录',
                'event_tags': []
            }
    
    def extract_topics(self, text: str) -> List[str]:
        """提取文本中的主要话题"""
        try:
            analysis = self.analyze_text(text)
            topics = []
            
            for delta in analysis.get('profile_deltas', []):
                topic = delta.get('attributes', {}).get('topic')
                if topic and topic not in topics:
                    topics.append(topic)
            
            return topics
            
        except Exception as e:
            logger.error(f"💥 提取话题失败: {e}")
            return []
    
    def extract_tags(self, text: str) -> List[Dict]:
        """提取文本中的标签"""
        try:
            analysis = self.analyze_text(text)
            return analysis.get('event_tags', [])
            
        except Exception as e:
            logger.error(f"💥 提取标签失败: {e}")
            return []


# 全局服务实例
memory_analyzer = None

def get_memory_analyzer() -> MemoryAnalyzer:
    """获取记忆分析器实例"""
    global memory_analyzer
    if memory_analyzer is None:
        memory_analyzer = MemoryAnalyzer()
    return memory_analyzer
