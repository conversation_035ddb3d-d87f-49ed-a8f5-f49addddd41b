import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from config import Config
from clients.volcengine_client import get_volcengine_client
from services.prompt_manager import PromptManager

# 配置日志
logger = logging.getLogger(__name__)

class VolcengineLLMService:
    def __init__(self):
        self.model = Config.VOLCENGINE_MODEL
        self.prompt_manager = PromptManager()

        # 创建火山引擎客户端
        self.client = get_volcengine_client()

        # 记忆分析相关配置
        self.topics = self._load_topics()
        self.tags = self._load_tags()

    def generate_response(
        self,
        user_message: str,
        system_prompt: str,
        conversation_history: List[Dict] = None,
        temperature: float = 0.7
    ) -> str:
        """
        调用LLM生成回复

        Args:
            user_message: 用户消息
            system_prompt: 系统提示词
            conversation_history: 对话历史
            temperature: 温度参数

        Returns:
            生成的回复文本
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        try:
            # 构建消息列表
            messages = [{"role": "system", "content": system_prompt}]

            # 添加对话历史
            if conversation_history:
                for conv in conversation_history[-10:]:  # 只保留最近10轮对话
                    role = "user" if conv['sender'] == 'user' else "assistant"
                    messages.append({"role": role, "content": conv['message']})

            # 添加当前用户消息
            messages.append({"role": "user", "content": user_message})

            # 记录请求日志
            logger.info(f"🚀 LLM请求 [{request_id}]")
            logger.info(f"📝 用户消息: {user_message}")
            logger.info(f"🎭 系统提示词: {system_prompt}")
            logger.info(f"📚 对话历史: {len(conversation_history) if conversation_history else 0} 条")
            logger.info(f"⚙️  请求参数: model={self.model}, temperature={temperature}")

            # 调用Chat Completions客户端
            result = self.client.chat_completions(
                messages=messages,
                model=self.model,
                temperature=temperature,
                max_tokens=1000
            )

            if result["success"]:
                generated_response = result["content"]

                # 记录成功响应日志
                logger.info(f"✅ LLM响应成功 [{request_id}]")
                logger.info(f"💬 生成回复: {generated_response}")
                logger.info(f"📊 响应统计: {len(generated_response)} 字符")

                return generated_response
            else:
                # 记录错误日志
                logger.error(f"❌ LLM API错误 [{request_id}]")
                logger.error(f"错误信息: {result.get('error', '未知错误')}")

                fallback_response = self._get_fallback_response()
                logger.info(f"🔄 使用备用回复: {fallback_response}")
                return fallback_response

        except Exception as e:
            # 记录异常日志
            logger.error(f"💥 LLM服务异常 [{request_id}]")
            logger.error(f"异常类型: {type(e).__name__}")
            logger.error(f"异常信息: {str(e)}")

            fallback_response = self._get_fallback_response()
            logger.info(f"🔄 使用备用回复: {fallback_response}")
            return fallback_response





    def analyze_emotion(self, text: str) -> Dict[str, float]:
        """
        分析文本情感

        Args:
            text: 要分析的文本

        Returns:
            情感分析结果，确保intensity是浮点数
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")

        try:
            logger.info(f"🎭 情感分析请求 [{request_id}] - analyze_emotion()")
            logger.info(f"📝 分析文本: {text}")

            # 使用提示词模块获取情感分析提示词
            system_prompt = self.prompt_manager.render_template('emotion_analysis.j2')

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"请分析这段文本的情感：{text}"}
            ]

            # 调用Chat Completions客户端
            result = self.client.chat_completions(
                messages=messages,
                model=self.model,
                temperature=0.3,
                max_tokens=200
            )

            if result["success"]:
                content = result["content"]
                logger.info(f"📄 LLM原始响应: {content}")

                # 尝试解析JSON
                try:
                    emotion_data = json.loads(content)

                    # 确保intensity是浮点数
                    if isinstance(emotion_data.get('intensity'), str):
                        try:
                            emotion_data['intensity'] = float(emotion_data['intensity'])
                        except (ValueError, TypeError):
                            emotion_data['intensity'] = 0.5

                    logger.info(f"✅ 情感分析成功 [{request_id}] - analyze_emotion()")
                    logger.info(f"😊 情感结果: {emotion_data}")
                    return emotion_data
                except json.JSONDecodeError:
                    logger.warning(f"⚠️  JSON解析失败，尝试清理内容")
                    try:
                        # 移除可能的markdown代码块格式
                        import re
                        cleaned_content = re.sub(r'```(?:json)?\s*([\s\S]*?)\s*```', r'\1', content)
                        emotion_data = json.loads(cleaned_content)

                        # 确保intensity是浮点数
                        if isinstance(emotion_data.get('intensity'), str):
                            try:
                                emotion_data['intensity'] = float(emotion_data['intensity'])
                            except (ValueError, TypeError):
                                emotion_data['intensity'] = 0.5

                        logger.info(f"✅ 清理后解析成功")
                        return emotion_data
                    except:
                        # 如果仍然失败，返回默认值
                        return {"emotion": "neutral", "intensity": 0.5, "keywords": []}
            else:
                logger.error(f"❌ 情感分析API错误: {result.get('error', '未知错误')}")

        except Exception as e:
            logger.error(f"💥 情感分析异常 [{request_id}]: {str(e)}")

        # 返回默认情感分析结果
        default_result = {"emotion": "neutral", "intensity": 0.5, "keywords": []}
        logger.info(f"🔄 使用默认情感分析结果: {default_result}")
        return default_result

    def split_message_segments(self, text: str) -> List[str]:
        """
        使用LLM将消息分段，模拟微信聊天习惯

        Args:
            text: 要分段的文本

        Returns:
            分段后的消息列表
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")

        try:
            logger.info(f"✂️  消息分段请求 [{request_id}]")
            logger.info(f"📝 原始文本: {text}")

            # 使用提示词模块获取消息分段提示词
            system_prompt = self.prompt_manager.render_template('message_segmentation.j2')

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"请将这段话分成多条微信消息：{text}"}
            ]

            # 调用Chat Completions客户端
            result = self.client.chat_completions(
                messages=messages,
                model=self.model,
                temperature=0.3,
                max_tokens=300
            )

            if result["success"]:
                content = result["content"]
                logger.info(f"📄 LLM分段响应: {content}")

                # 按换行符分割，过滤空行
                segments = [seg.strip() for seg in content.split('\n') if seg.strip()]

                logger.info(f"✅ 消息分段成功 [{request_id}]")
                logger.info(f"📊 分段结果: {len(segments)} 段")
                for i, seg in enumerate(segments, 1):
                    logger.info(f"  {i}. {seg}")

                return segments
            else:
                logger.error(f"❌ 消息分段API错误 [{request_id}]: {result.get('error', '未知错误')}")

        except Exception as e:
            logger.error(f"💥 消息分段异常 [{request_id}]: {str(e)}")

        # 返回空列表，让调用方使用备用方案
        logger.info(f"🔄 消息分段失败，返回空列表使用备用方案")
        return []

    def analyze_memory(self, text: str, user_id: str = None) -> Dict:
        """
        分析文本，提取记忆信息

        Args:
            text: 要分析的文本
            user_id: 用户ID（可选）

        Returns:
            分析结果字典，包含profile_deltas、event_tip、event_tags
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")

        try:
            logger.info(f"🧠 记忆分析请求 [{request_id}]")
            logger.info(f"📝 分析文本: {text}")

            # 使用提示词模板
            topics_str = ", ".join(self.topics)
            tags_str = ", ".join(self.tags)

            system_prompt = self.prompt_manager.render_template(
                'memory_extraction.j2',
                text=text,
                topics_str=topics_str,
                tags_str=tags_str
            )

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"请分析这段文本：{text}"}
            ]

            # 调用Chat Completions客户端
            result = self.client.chat_completions(
                messages=messages,
                model=self.model,
                temperature=0.3,
                max_tokens=500
            )

            if result["success"]:
                content = result["content"]
                logger.info(f"📄 LLM原始响应: {content}")

                # 解析LLM响应
                analysis = self._parse_memory_response(content)

                logger.info(f"✅ 记忆分析成功 [{request_id}]: {len(analysis.get('profile_deltas', []))} 个画像")
                return analysis
            else:
                logger.error(f"❌ 记忆分析API错误 [{request_id}]: {result.get('error', '未知错误')}")
                return self._fallback_memory_analysis(text)

        except Exception as e:
            logger.error(f"💥 记忆分析异常 [{request_id}]: {str(e)}")
            return self._fallback_memory_analysis(text)

    def _load_topics(self) -> List[str]:
        """加载预定义的topic列表"""
        return [
            "basic_info",      # 基本信息
            "personality",     # 性格特点
            "interests",       # 兴趣爱好
            "experience",      # 经历体验
            "emotion",         # 情感状态
            "relationship",    # 人际关系
            "goal",           # 目标计划
            "preference",     # 偏好选择
            "skill",          # 技能能力
            "lifestyle"       # 生活方式
        ]

    def _load_tags(self) -> List[str]:
        """加载预定义的tag列表"""
        return [
            "positive",       # 积极的
            "negative",       # 消极的
            "important",      # 重要的
            "casual",         # 随意的
            "private",        # 私密的
            "public",         # 公开的
            "recent",         # 最近的
            "past",           # 过去的
            "future",         # 未来的
            "factual",        # 事实性的
            "emotional",      # 情感性的
            "opinion"         # 观点性的
        ]

    def _parse_memory_response(self, response: str) -> Dict:
        """解析记忆分析LLM响应"""
        try:
            # 尝试直接解析JSON
            analysis = json.loads(response.strip())

            # 验证格式
            if not isinstance(analysis, dict):
                raise ValueError("响应不是字典格式")

            # 确保必要字段存在
            analysis.setdefault('profile_deltas', [])
            analysis.setdefault('event_tip', '')
            analysis.setdefault('event_tags', [])

            # 验证profile_deltas格式
            valid_deltas = []
            for delta in analysis.get('profile_deltas', []):
                if isinstance(delta, dict) and 'content' in delta:
                    # 确保attributes存在
                    if 'attributes' not in delta:
                        delta['attributes'] = {}

                    # 验证topic
                    topic = delta['attributes'].get('topic', 'basic_info')
                    if topic not in self.topics:
                        delta['attributes']['topic'] = 'basic_info'

                    # 设置默认confidence
                    if 'confidence' not in delta['attributes']:
                        delta['attributes']['confidence'] = 0.7

                    valid_deltas.append(delta)

            analysis['profile_deltas'] = valid_deltas
            return analysis

        except Exception as e:
            logger.error(f"💥 解析记忆分析响应失败: {e}")
            return self._fallback_memory_analysis(response)

    def _fallback_memory_analysis(self, text: str) -> Dict:
        """备用记忆分析方法（基于规则）"""
        try:
            profile_deltas = []
            event_tags = []

            # 简单的关键词匹配
            keywords_map = {
                'basic_info': ['我是', '我叫', '我的名字', '我今年', '岁', '我住在', '我来自'],
                'interests': ['喜欢', '爱好', '兴趣', '热爱', '痴迷'],
                'emotion': ['开心', '难过', '高兴', '郁闷', '兴奋', '紧张', '焦虑'],
                'experience': ['去过', '做过', '经历', '体验', '参加'],
                'personality': ['性格', '脾气', '个性', '特点']
            }

            for topic, keywords in keywords_map.items():
                for keyword in keywords:
                    if keyword in text:
                        # 提取包含关键词的句子
                        sentences = text.split('。')
                        for sentence in sentences:
                            if keyword in sentence and len(sentence.strip()) > 3:
                                profile_deltas.append({
                                    'content': sentence.strip(),
                                    'attributes': {
                                        'topic': topic,
                                        'sub_topic': 'extracted',
                                        'confidence': 0.6
                                    }
                                })
                                break
                        break

            # 添加情感标签
            if any(word in text for word in ['开心', '高兴', '兴奋']):
                event_tags.append({'tag': 'positive', 'value': 'happy'})
            elif any(word in text for word in ['难过', '郁闷', '焦虑']):
                event_tags.append({'tag': 'negative', 'value': 'sad'})

            return {
                'profile_deltas': profile_deltas[:3],  # 限制数量
                'event_tip': f"用户分享了关于{len(profile_deltas)}个方面的信息",
                'event_tags': event_tags
            }

        except Exception as e:
            logger.error(f"💥 备用记忆分析失败: {e}")
            return {
                'profile_deltas': [],
                'event_tip': '对话记录',
                'event_tags': []
            }

    def extract_topics(self, text: str) -> List[str]:
        """提取文本中的主要话题"""
        try:
            analysis = self.analyze_memory(text)
            topics = []

            for delta in analysis.get('profile_deltas', []):
                topic = delta.get('attributes', {}).get('topic')
                if topic and topic not in topics:
                    topics.append(topic)

            return topics

        except Exception as e:
            logger.error(f"💥 提取话题失败: {e}")
            return []

    def extract_tags(self, text: str) -> List[Dict]:
        """提取文本中的标签"""
        try:
            analysis = self.analyze_memory(text)
            return analysis.get('event_tags', [])

        except Exception as e:
            logger.error(f"💥 提取标签失败: {e}")
            return []

    def _get_fallback_response(self) -> str:
        """获取备用回复"""
        fallback_responses = [
            "抱歉，我刚才有点走神了，能再说一遍吗？",
            "不好意思，我需要一点时间整理思路，你可以稍等一下吗？",
            "我现在有点累了，不过还是很想听你说话，能换个话题吗？",
            "刚才网络好像有点问题，你刚才说什么了？"
        ]
        import random
        return random.choice(fallback_responses)


# 全局服务实例
llm_service = None

def get_llm_service() -> VolcengineLLMService:
    """获取LLM服务实例"""
    global llm_service
    if llm_service is None:
        llm_service = VolcengineLLMService()
    return llm_service


