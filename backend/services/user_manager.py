"""
用户管理服务
提供用户注册、登录、会话管理等业务功能
"""

import hashlib
import secrets
import re
import logging
from datetime import datetime
from typing import Dict, Optional
from models.memory import UserAuth, UserSession, User

# 配置日志
logger = logging.getLogger(__name__)

class UserManager:
    """用户管理器"""
    
    def __init__(self):
        """初始化用户管理器"""
        # 会话过期时间（7天）
        self.session_expire_days = 7

        # 确保相关表存在
        self._ensure_tables()
    
    def _ensure_tables(self):
        """确保用户相关表存在"""
        try:
            UserAuth.ensure_tables()
            UserSession.ensure_tables()
            logger.info("✅ 用户管理表已创建")
        except Exception as e:
            logger.error(f"💥 创建用户管理表失败: {e}")
    
    def create_user(self, username: str, password: str = None, nickname: str = None, 
                   email: str = None) -> Dict:
        """创建新用户"""
        try:
            # 验证用户名
            if not self._validate_username(username):
                raise ValueError("用户名格式不正确（3-20个字符，只允许字母、数字、下划线）")
            
            # 检查用户名是否已存在
            if UserAuth.username_exists(username):
                raise ValueError("用户名已存在")
            
            # 处理密码
            password_hash = None
            if password:
                if not self._validate_password(password):
                    raise ValueError("密码格式不正确（6-50个字符）")
                password_hash = self._hash_password(password)
            
            # 验证邮箱
            if email and not self._validate_email(email):
                raise ValueError("邮箱格式不正确")
            
            # 创建用户认证记录
            user_auth = UserAuth(username, password_hash, email)
            user_id = user_auth.save()
            
            # 创建用户记录
            User.create(username, name=nickname or username)
            
            logger.info(f"✅ 用户创建成功 - 用户名: {username}, ID: {user_id}")
            
            return {
                'user_id': user_id,
                'username': username,
                'nickname': nickname or username,
                'email': email,
                'status': 'active',
                'created_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"💥 用户创建失败 - 用户名: {username}, 错误: {str(e)}")
            raise
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """用户认证"""
        try:
            # 查找用户
            user_auth = UserAuth.find_by_username(username)
            if not user_auth:
                logger.warning(f"⚠️  认证失败 - 用户不存在: {username}")
                return None
            
            # 检查用户状态
            if user_auth.status != 'active':
                logger.warning(f"⚠️  认证失败 - 用户状态异常: {username}, 状态: {user_auth.status}")
                return None
            
            # 验证密码
            if not user_auth.password_hash:
                logger.warning(f"⚠️  认证失败 - 用户未设置密码: {username}")
                return None
            
            if not self._verify_password(password, user_auth.password_hash):
                logger.warning(f"⚠️  认证失败 - 密码错误: {username}")
                return None
            
            logger.info(f"✅ 用户认证成功 - 用户名: {username}")
            
            return {
                'user_id': user_auth.user_id,
                'username': user_auth.username,
                'nickname': user_auth.username,  # 暂时使用username作为nickname
                'email': user_auth.email,
                'status': user_auth.status,
                'created_at': user_auth.created_at.isoformat() if user_auth.created_at else None
            }
            
        except Exception as e:
            logger.error(f"💥 用户认证异常 - 用户名: {username}, 错误: {str(e)}")
            return None
    
    def create_session(self, user_id: str) -> str:
        """创建用户会话"""
        try:
            session = UserSession(user_id)
            session_token = session.save()
            
            logger.info(f"✅ 会话创建成功 - 用户ID: {user_id}, 令牌: {session_token[:8]}...")
            
            return session_token
            
        except Exception as e:
            logger.error(f"💥 会话创建失败 - 用户ID: {user_id}, 错误: {str(e)}")
            raise
    
    def validate_session(self, session_token: str) -> Optional[Dict]:
        """验证会话"""
        try:
            session = UserSession.find_by_token(session_token)
            if not session:
                return None
            
            if not session.is_valid():
                logger.info(f"⚠️  会话无效 - 令牌: {session_token[:8]}...")
                return None
            
            return {
                'user_id': session.user_id,
                'username': session.user_info['username'],
                'nickname': session.user_info['username'],
                'email': session.user_info['email'],
                'status': session.user_info['status']
            }
            
        except Exception as e:
            logger.error(f"💥 会话验证异常 - 令牌: {session_token[:8]}..., 错误: {str(e)}")
            return None
    
    def logout_user(self, session_token: str) -> bool:
        """用户登出"""
        try:
            session = UserSession.find_by_token(session_token)
            if not session:
                return False
            
            result = session.deactivate()
            
            if result:
                logger.info(f"✅ 用户登出成功 - 令牌: {session_token[:8]}...")
            
            return result
        except Exception as e:
            logger.error(f"💥 用户登出失败 - 令牌: {session_token[:8]}..., 错误: {str(e)}")
            return False
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            salt, hash_value = password_hash.split(':')
            computed_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return computed_hash.hex() == hash_value
        except:
            return False
    
    def _validate_username(self, username: str) -> bool:
        """验证用户名格式"""
        if not username or len(username) < 3 or len(username) > 20:
            return False
        return re.match(r'^[a-zA-Z0-9_]+$', username) is not None
    
    def _validate_password(self, password: str) -> bool:
        """验证密码格式"""
        if not password or len(password) < 6 or len(password) > 50:
            return False
        return True
    
    def _validate_email(self, email: str) -> bool:
        """验证邮箱格式"""
        if not email:
            return True
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None


# 全局实例
user_manager = None

def get_user_manager() -> UserManager:
    """获取用户管理器实例"""
    global user_manager
    if user_manager is None:
        user_manager = UserManager()
    return user_manager
