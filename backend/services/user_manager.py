"""
用户管理模块
提供用户注册、登录、管理等功能
"""

import hashlib
import secrets
import re
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from models.database import DatabaseManager

# 配置日志
logger = logging.getLogger(__name__)

class UserManager:
    """用户管理器"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """
        初始化用户管理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db = db_manager or DatabaseManager()
        
        # 用户状态定义
        self.user_status = {
            'active': '活跃',
            'inactive': '非活跃',
            'banned': '已封禁',
            'deleted': '已删除'
        }
        
        # 会话过期时间（7天）
        self.session_expire_days = 7
    
    def create_user(self, username: str, password: str = None, nickname: str = None, 
                   email: str = None) -> Dict:
        """
        创建新用户
        
        Args:
            username: 用户名（唯一标识）
            password: 密码（可选，用于需要认证的场景）
            nickname: 昵称（可选）
            email: 邮箱（可选）
            
        Returns:
            创建的用户信息
        """
        try:
            # 验证用户名
            if not self._validate_username(username):
                raise ValueError("用户名格式不正确")
            
            # 检查用户名是否已存在
            if self._username_exists(username):
                raise ValueError("用户名已存在")
            
            # 生成用户ID
            user_id = self._generate_user_id()
            
            # 处理密码
            password_hash = None
            if password:
                if not self._validate_password(password):
                    raise ValueError("密码格式不正确")
                password_hash = self._hash_password(password)
            
            # 验证邮箱
            if email and not self._validate_email(email):
                raise ValueError("邮箱格式不正确")
            
            # 创建用户
            user_data = {
                'user_id': user_id,
                'username': username,
                'password_hash': password_hash,
                'nickname': nickname or username,
                'email': email,
                'status': 'active',
                'created_at': datetime.now().isoformat(),
                'last_active': datetime.now().isoformat()
            }
            
            self.db.create_user(user_data)
            
            logger.info(f"✅ 用户创建成功 - 用户名: {username}, ID: {user_id}")
            
            # 返回用户信息（不包含密码）
            return {
                'user_id': user_id,
                'username': username,
                'nickname': user_data['nickname'],
                'email': email,
                'status': 'active',
                'created_at': user_data['created_at']
            }
            
        except Exception as e:
            logger.error(f"💥 用户创建失败 - 用户名: {username}, 错误: {str(e)}")
            raise
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            认证成功返回用户信息，失败返回None
        """
        try:
            user = self.db.get_user_by_username(username)
            if not user:
                logger.warning(f"⚠️  认证失败 - 用户不存在: {username}")
                return None
            
            # 检查用户状态
            if user.get('status') != 'active':
                logger.warning(f"⚠️  认证失败 - 用户状态异常: {username}, 状态: {user.get('status')}")
                return None
            
            # 验证密码
            if not user.get('password_hash'):
                logger.warning(f"⚠️  认证失败 - 用户未设置密码: {username}")
                return None
            
            if not self._verify_password(password, user['password_hash']):
                logger.warning(f"⚠️  认证失败 - 密码错误: {username}")
                return None
            
            # 更新最后活跃时间
            self.update_last_active(user['user_id'])
            
            logger.info(f"✅ 用户认证成功 - 用户名: {username}")
            
            # 返回用户信息（不包含密码）
            return {
                'user_id': user['user_id'],
                'username': user['username'],
                'nickname': user['nickname'],
                'email': user['email'],
                'status': user['status'],
                'created_at': user['created_at'],
                'last_active': user['last_active']
            }
            
        except Exception as e:
            logger.error(f"💥 用户认证异常 - 用户名: {username}, 错误: {str(e)}")
            return None
    
    def create_session(self, user_id: str) -> str:
        """
        创建用户会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            会话令牌
        """
        try:
            # 生成会话令牌
            session_token = self._generate_session_token()
            
            # 计算过期时间
            expires_at = datetime.now() + timedelta(days=self.session_expire_days)
            
            # 保存会话
            session_data = {
                'session_token': session_token,
                'user_id': user_id,
                'created_at': datetime.now().isoformat(),
                'expires_at': expires_at.isoformat(),
                'is_active': True
            }
            
            self.db.create_session(session_data)
            
            logger.info(f"✅ 会话创建成功 - 用户ID: {user_id}, 令牌: {session_token[:8]}...")
            
            return session_token
            
        except Exception as e:
            logger.error(f"💥 会话创建失败 - 用户ID: {user_id}, 错误: {str(e)}")
            raise
    
    def validate_session(self, session_token: str) -> Optional[Dict]:
        """
        验证会话
        
        Args:
            session_token: 会话令牌
            
        Returns:
            有效会话返回用户信息，无效返回None
        """
        try:
            session = self.db.get_session(session_token)
            if not session:
                return None
            
            # 检查会话是否过期
            expires_at = datetime.fromisoformat(session['expires_at'])
            if datetime.now() > expires_at:
                logger.info(f"⚠️  会话已过期 - 令牌: {session_token[:8]}...")
                return None
            
            # 检查会话是否活跃
            if not session.get('is_active'):
                logger.info(f"⚠️  会话已失效 - 令牌: {session_token[:8]}...")
                return None
            
            # 获取用户信息
            user = self.db.get_user_by_id(session['user_id'])
            if not user or user.get('status') != 'active':
                logger.warning(f"⚠️  会话对应用户状态异常 - 用户ID: {session['user_id']}")
                return None
            
            # 更新最后活跃时间
            self.update_last_active(user['user_id'])
            
            return {
                'user_id': user['user_id'],
                'username': user['username'],
                'nickname': user['nickname'],
                'email': user['email'],
                'status': user['status']
            }
            
        except Exception as e:
            logger.error(f"💥 会话验证异常 - 令牌: {session_token[:8]}..., 错误: {str(e)}")
            return None
    
    def logout_user(self, session_token: str) -> bool:
        """
        用户登出
        
        Args:
            session_token: 会话令牌
            
        Returns:
            登出是否成功
        """
        try:
            result = self.db.deactivate_session(session_token)
            if result:
                logger.info(f"✅ 用户登出成功 - 令牌: {session_token[:8]}...")
            return result
        except Exception as e:
            logger.error(f"💥 用户登出失败 - 令牌: {session_token[:8]}..., 错误: {str(e)}")
            return False
    
    def get_user_info(self, user_id: str) -> Optional[Dict]:
        """
        获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户信息
        """
        try:
            user = self.db.get_user_by_id(user_id)
            if not user:
                return None
            
            return {
                'user_id': user['user_id'],
                'username': user['username'],
                'nickname': user['nickname'],
                'email': user['email'],
                'status': user['status'],
                'created_at': user['created_at'],
                'last_active': user['last_active']
            }
        except Exception as e:
            logger.error(f"💥 获取用户信息失败 - 用户ID: {user_id}, 错误: {str(e)}")
            return None
    
    def update_user_info(self, user_id: str, **kwargs) -> bool:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            **kwargs: 要更新的字段
            
        Returns:
            更新是否成功
        """
        try:
            # 过滤允许更新的字段
            allowed_fields = ['nickname', 'email']
            update_data = {k: v for k, v in kwargs.items() if k in allowed_fields}
            
            if not update_data:
                return False
            
            # 验证邮箱格式
            if 'email' in update_data and update_data['email']:
                if not self._validate_email(update_data['email']):
                    raise ValueError("邮箱格式不正确")
            
            result = self.db.update_user(user_id, update_data)
            if result:
                logger.info(f"✅ 用户信息更新成功 - 用户ID: {user_id}, 更新字段: {list(update_data.keys())}")
            return result
        except Exception as e:
            logger.error(f"💥 用户信息更新失败 - 用户ID: {user_id}, 错误: {str(e)}")
            return False
    
    def update_last_active(self, user_id: str):
        """更新用户最后活跃时间"""
        try:
            self.db.update_user_last_active(user_id)
        except Exception as e:
            logger.error(f"💥 更新最后活跃时间失败 - 用户ID: {user_id}, 错误: {str(e)}")
    
    def list_users(self, page: int = 1, page_size: int = 20, status: str = None) -> Dict:
        """
        获取用户列表
        
        Args:
            page: 页码
            page_size: 每页数量
            status: 用户状态过滤
            
        Returns:
            用户列表和分页信息
        """
        try:
            offset = (page - 1) * page_size
            users, total = self.db.get_users_list(offset, page_size, status)
            
            return {
                'users': users,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total': total,
                    'total_pages': (total + page_size - 1) // page_size
                }
            }
        except Exception as e:
            logger.error(f"💥 获取用户列表失败 - 错误: {str(e)}")
            return {'users': [], 'pagination': {'page': 1, 'page_size': page_size, 'total': 0, 'total_pages': 0}}
    
    def _generate_user_id(self) -> str:
        """
        生成用户ID

        为了兼容 Memobase，生成标准 UUID 格式的用户ID
        """
        import uuid
        # 生成标准UUID格式的用户ID
        user_uuid = str(uuid.uuid4())
        logger.info(f"🆔 生成新用户ID: {user_uuid}")
        return user_uuid
    
    def _generate_session_token(self) -> str:
        """生成会话令牌"""
        return secrets.token_urlsafe(32)
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            salt, hash_value = password_hash.split(':')
            computed_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return computed_hash.hex() == hash_value
        except:
            return False
    
    def _validate_username(self, username: str) -> bool:
        """验证用户名格式"""
        if not username or len(username) < 3 or len(username) > 20:
            return False
        # 只允许字母、数字、下划线
        return re.match(r'^[a-zA-Z0-9_]+$', username) is not None
    
    def _validate_password(self, password: str) -> bool:
        """验证密码格式"""
        if not password or len(password) < 6 or len(password) > 50:
            return False
        return True
    
    def _validate_email(self, email: str) -> bool:
        """验证邮箱格式"""
        if not email:
            return True  # 邮箱是可选的
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def _username_exists(self, username: str) -> bool:
        """检查用户名是否已存在"""
        return self.db.get_user_by_username(username) is not None
