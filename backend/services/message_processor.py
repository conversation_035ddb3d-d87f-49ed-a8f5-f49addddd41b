"""
消息处理模块
负责消息分段、标点符号处理等消息加工逻辑
"""

import re
from typing import List, Dict, Any
from config import Config


class MessageProcessor:
    """消息处理器 - 负责消息分段和标点符号处理"""
    
    def __init__(self, llm_service=None):
        """
        初始化消息处理器
        
        Args:
            llm_service: LLM服务实例，用于智能分段
        """
        self.llm_service = llm_service
        self.config = Config.MESSAGE_SEGMENTATION_CONFIG
    
    def process_message(self, message: str) -> List[str]:
        """
        处理消息：分段 + 标点符号清理
        
        Args:
            message: 原始消息文本
            
        Returns:
            处理后的消息段列表
        """
        if not message or not message.strip():
            return [message]

        # 清理文本
        text = message.strip()

        # 如果文本很短，只清理标点符号，不分段
        if len(text) <= 10:
            return self._clean_punctuation([text])

        # 根据配置决定分段方式
        if self.config['use_llm_split'] and self.llm_service:
            # 使用LLM进行智能分段
            try:
                segments = self._llm_split(text)
                if segments and len(segments) > 1:
                    return segments
            except Exception as e:
                print(f"LLM分段失败，使用备用方案: {e}")

        # 备用方案：规则分段
        return self._rule_based_split(text)
    
    def _llm_split(self, text: str) -> List[str]:
        """使用LLM智能分段"""
        if not self.llm_service:
            return []
            
        try:
            # 调用LLM服务的分段方法
            segments = self.llm_service.split_message_segments(text)
            
            if segments and self._validate_segments(segments, text):
                # 清理标点符号
                return self._clean_punctuation(segments)
                
        except Exception as e:
            print(f"LLM分段调用失败: {e}")
            
        return []
    
    def _rule_based_split(self, text: str) -> List[str]:
        """基于规则的分段方案，保护情绪表达"""
        max_length = self.config['fallback_max_length']
        
        # 检查是否包含情绪表达
        emotion_matches = self._find_emotion_expressions(text)
        
        if not emotion_matches:
            # 没有情绪表达，使用普通分段并清理标点
            segments = self._simple_split(text, max_length)
            return self._clean_punctuation(segments)
        
        # 有情绪表达时，保护这些表达不被分割
        protected_segments = self._split_with_emotion_protection(text, emotion_matches, max_length)
        return self._clean_punctuation(protected_segments)
    
    def _find_emotion_expressions(self, text: str) -> List[tuple]:
        """查找文本中的情绪表达"""
        emotion_patterns = [
            r'[！!]{2,}',  # 多个感叹号
            r'[？?]{2,}',  # 多个问号  
            r'[。.]{2,}',  # 多个句号
        ]
        
        emotion_matches = []
        for pattern in emotion_patterns:
            for match in re.finditer(pattern, text):
                emotion_matches.append((match.start(), match.end(), match.group()))
        
        return emotion_matches
    
    def _split_with_emotion_protection(self, text: str, emotion_matches: List[tuple], max_length: int) -> List[str]:
        """在保护情绪表达的前提下分段"""
        # 用占位符替换情绪表达
        protected_text = text
        placeholders = {}
        
        for i, (start, end, emotion) in enumerate(emotion_matches):
            placeholder = f"__EMOTION_{i}__"
            placeholders[placeholder] = emotion
            protected_text = protected_text[:start] + placeholder + protected_text[end:]
            
            # 更新后续匹配的位置
            offset = len(placeholder) - len(emotion)
            for j in range(i + 1, len(emotion_matches)):
                if emotion_matches[j][0] > start:
                    emotion_matches[j] = (
                        emotion_matches[j][0] + offset, 
                        emotion_matches[j][1] + offset, 
                        emotion_matches[j][2]
                    )
        
        # 对保护后的文本进行分段
        segments = self._simple_split(protected_text, max_length)
        
        # 恢复情绪表达
        final_segments = []
        for segment in segments:
            for placeholder, emotion in placeholders.items():
                segment = segment.replace(placeholder, emotion)
            final_segments.append(segment)
        
        return final_segments
    
    def _simple_split(self, text: str, max_length: int) -> List[str]:
        """简单的分段逻辑"""
        # 按句号、问号、感叹号分段
        sentences = re.split(r'([。！？!?])', text)

        segments = []
        current = ""

        for i in range(0, len(sentences), 2):
            sentence = sentences[i] if i < len(sentences) else ""
            punctuation = sentences[i + 1] if i + 1 < len(sentences) else ""

            if not sentence.strip():
                continue

            full_sentence = sentence + punctuation

            # 如果累积长度合适，合并；否则分段
            if len(current + full_sentence) <= max_length and current:
                current += full_sentence
            else:
                if current:
                    segments.append(current.strip())
                current = full_sentence

        if current.strip():
            segments.append(current.strip())

        return segments if segments else [text]
    
    def _clean_punctuation(self, segments: List[str]) -> List[str]:
        """智能清理标点符号，保留情绪表达"""
        cleaned_segments = []
        
        for segment in segments:
            segment = segment.strip()
            
            # 检查是否是情绪表达（连续2个或以上相同标点符号）
            if re.search(r'([！!]{2,}|[？?]{2,}|[。.]{2,})$', segment):
                # 保留情绪表达
                cleaned_segments.append(segment)
            else:
                # 删除单个句末标点符号
                cleaned_segment = re.sub(r'[。！？!?.]$', '', segment)
                if cleaned_segment:
                    cleaned_segments.append(cleaned_segment)
                else:
                    # 如果清理后为空，保留原文
                    cleaned_segments.append(segment)
        
        return cleaned_segments
    
    def _validate_segments(self, segments: List[str], original: str) -> bool:
        """验证分段结果的合理性"""
        if not segments:
            return False
            
        # 检查总长度是否合理（允许一定差异，考虑到会删除标点符号）
        total_length = sum(len(seg) for seg in segments)
        if abs(total_length - len(original)) > len(original) * 0.4:
            return False
            
        # 检查是否有过长的段落
        for seg in segments:
            if len(seg) > 50:  # 单段不应该太长
                return False
                
        return True
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            'use_llm_split': self.config['use_llm_split'],
            'fallback_max_length': self.config['fallback_max_length'],
            'min_segments': self.config['min_segments'],
            'max_segments': self.config['max_segments'],
        }


class MessageSegmentationTester:
    """消息分段测试器"""
    
    def __init__(self, processor: MessageProcessor):
        self.processor = processor
    
    def test_cases(self, test_cases: List[str]) -> Dict[str, Any]:
        """测试多个用例"""
        results = []
        
        for i, text in enumerate(test_cases, 1):
            segments = self.processor.process_message(text)
            results.append({
                'case_id': i,
                'original': text,
                'segments': segments,
                'segment_count': len(segments),
                'total_length': sum(len(seg) for seg in segments),
                'original_length': len(text)
            })
        
        return {
            'test_results': results,
            'total_cases': len(test_cases),
            'processor_config': self.processor.get_processing_stats()
        }
    
    def print_test_results(self, test_cases: List[str]):
        """打印测试结果"""
        print("=== 消息处理测试结果 ===\n")
        
        for i, text in enumerate(test_cases, 1):
            segments = self.processor.process_message(text)
            
            print(f"测试用例 {i}:")
            print(f"原始消息: {text}")
            print(f"分段结果 ({len(segments)} 段):")
            for j, segment in enumerate(segments, 1):
                print(f"  {j}. {segment}")
            print("-" * 50)
