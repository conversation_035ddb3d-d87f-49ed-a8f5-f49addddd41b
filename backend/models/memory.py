"""
简化的本地记忆模块
直连PostgreSQL，适配现有Memobase表结构
"""

import os
import json
import psycopg2
import psycopg2.extras
from typing import Optional, Dict, List
from contextlib import contextmanager
from datetime import datetime

# PostgreSQL数据库连接配置
DATABASE_URL = os.getenv('MEMORY_DATABASE_URL', 'postgresql://memobase:memobase123@localhost:5433/memobase')

# 固定的项目ID，用于我们的虚拟人应用
PROJECT_ID = "virtual_companion_app"


@contextmanager
def get_db_connection():
    """获取数据库连接"""
    conn = psycopg2.connect(DATABASE_URL)
    try:
        yield conn
    finally:
        conn.close()


def init_database():
    """数据库初始化，确保项目和必要表存在"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 确保项目存在（如果projects表存在的话）
            try:
                cursor.execute('''
                    INSERT INTO projects (project_id, project_secret, status, id, created_at, updated_at)
                    VALUES (%s, %s, %s, gen_random_uuid(), NOW(), NOW())
                    ON CONFLICT (project_id) DO NOTHING
                ''', (PROJECT_ID, 'secret123', 'active'))
                conn.commit()
                print(f"✅ 项目 {PROJECT_ID} 已确保存在")
            except Exception as e:
                print(f"⚠️ 项目初始化跳过: {e}")

            # 创建必要的表
            _ensure_table_exists(cursor, 'conversations')
            _ensure_table_exists(cursor, 'affection_levels')
            _ensure_table_exists(cursor, 'persona_states')
            conn.commit()
            print("✅ 必要表已创建")

    except Exception as e:
        print(f"⚠️ 数据库初始化失败: {e}")


def _ensure_table_exists(cursor, table_name: str):
    """按需创建表"""
    if table_name == 'conversations':
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversations (
                id SERIAL PRIMARY KEY,
                user_id UUID NOT NULL,
                project_id VARCHAR(64) NOT NULL DEFAULT %s,
                message TEXT NOT NULL,
                sender VARCHAR(20) NOT NULL,
                emotion_score FLOAT DEFAULT 0.0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''', (PROJECT_ID,))
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id, project_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversations_timestamp ON conversations(timestamp)')

    elif table_name == 'affection_levels':
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS affection_levels (
                id SERIAL PRIMARY KEY,
                user_id UUID NOT NULL,
                project_id VARCHAR(64) NOT NULL DEFAULT %s,
                level INTEGER NOT NULL,
                change_reason TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''', (PROJECT_ID,))
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_affection_user_id ON affection_levels(user_id, project_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_affection_timestamp ON affection_levels(timestamp)')

    elif table_name == 'persona_states':
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS persona_states (
                id SERIAL PRIMARY KEY,
                project_id VARCHAR(64) NOT NULL DEFAULT %s,
                date DATE NOT NULL,
                current_activity TEXT,
                mood TEXT,
                work_content TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''', (PROJECT_ID,))
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_persona_states_date ON persona_states(date, project_id)')


def _get_user_uuid(cursor, user_id: str) -> Optional[str]:
    """获取用户的UUID（内部辅助方法）"""
    cursor.execute('''
        SELECT id FROM users
        WHERE project_id = %s
        AND additional_fields->>'user_id' = %s
    ''', (PROJECT_ID, user_id))
    result = cursor.fetchone()
    return str(result[0]) if result else None


class User:
    """用户模型类"""

    def __init__(self, user_id: str = None, username: str = None,
                 name: str = None, additional_fields: Dict = None):
        self.user_id = user_id
        self.username = username
        self.name = name
        self.additional_fields = additional_fields or {}
        self.project_id = PROJECT_ID

    @classmethod
    def create(cls, user_id: str, name: str = None, additional_data: Dict = None):
        """创建新用户"""
        with get_db_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 尝试获取现有用户
            cursor.execute('''
                SELECT * FROM users
                WHERE project_id = %s
                AND additional_fields->>'user_id' = %s
            ''', (PROJECT_ID, user_id))
            user = cursor.fetchone()

            if user:
                return cls.from_dict(dict(user))

            # 创建新用户
            additional_fields = additional_data or {}
            additional_fields['user_id'] = user_id
            additional_fields['name'] = name or f'用户{user_id[:8]}'

            cursor.execute('''
                INSERT INTO users (id, project_id, additional_fields)
                VALUES (gen_random_uuid(), %s, %s)
                RETURNING *
            ''', (PROJECT_ID, json.dumps(additional_fields)))

            user_data = cursor.fetchone()
            conn.commit()
            return cls.from_dict(dict(user_data))

    @classmethod
    def from_dict(cls, data: Dict):
        """从字典创建用户对象"""
        additional_fields = data.get('additional_fields', {})
        return cls(
            user_id=additional_fields.get('user_id'),
            username=additional_fields.get('user_id'),
            name=additional_fields.get('name'),
            additional_fields=additional_fields
        )

    def get_uuid(self) -> Optional[str]:
        """获取用户的UUID"""
        return _get_user_uuid_by_id(self.user_id)

    @classmethod
    def get_or_create(cls, user_id: str, name: str = None) -> 'User':
        """获取或创建用户"""
        return cls.create(user_id, name)


def _get_user_uuid_by_id(user_id: str) -> Optional[str]:
    """根据user_id获取UUID"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        return _get_user_uuid(cursor, user_id)


class Conversation:
    """对话模型类"""

    def __init__(self, user_id: str = None, message: str = None,
                 sender: str = None, emotion_score: float = 0.0, timestamp: datetime = None):
        self.user_id = user_id
        self.message = message
        self.sender = sender
        self.emotion_score = emotion_score
        self.timestamp = timestamp
        self.project_id = PROJECT_ID

    def save(self):
        """保存对话记录"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(self.user_id)
            if not user_uuid:
                # 如果用户不存在，先创建用户
                User.create(self.user_id)
                user_uuid = _get_user_uuid_by_id(self.user_id)

            cursor.execute('''
                INSERT INTO conversations (user_id, project_id, message, sender, emotion_score)
                VALUES (%s, %s, %s, %s, %s)
            ''', (user_uuid, self.project_id, self.message, self.sender, self.emotion_score))

            conn.commit()

    @classmethod
    def get_recent(cls, user_id: str, limit: int = 10) -> List[Dict]:
        """获取最近对话"""
        with get_db_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(user_id)
            if not user_uuid:
                return []

            cursor.execute('''
                SELECT * FROM conversations
                WHERE user_id = %s AND project_id = %s
                ORDER BY timestamp DESC LIMIT %s
            ''', (user_uuid, PROJECT_ID, limit))

            return [dict(row) for row in cursor.fetchall()]


class AffectionLevel:
    """好感度模型类"""

    def __init__(self, user_id: str = None, level: int = 10,
                 change_reason: str = None):
        self.user_id = user_id
        self.level = level
        self.change_reason = change_reason
        self.project_id = PROJECT_ID

    def update(self, change: int, reason: str) -> int:
        """更新好感度"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(self.user_id)
            if not user_uuid:
                # 如果用户不存在，先创建用户
                User.create(self.user_id)
                user_uuid = _get_user_uuid_by_id(self.user_id)

            # 获取当前好感度
            cursor.execute('''
                SELECT level FROM affection_levels
                WHERE user_id = %s AND project_id = %s
                ORDER BY timestamp DESC LIMIT 1
            ''', (user_uuid, self.project_id))

            result = cursor.fetchone()
            current_level = result[0] if result else 10  # 默认初始好感度

            # 计算新好感度
            new_level = max(0, min(100, current_level + change))

            # 记录好感度变化
            cursor.execute('''
                INSERT INTO affection_levels (user_id, project_id, level, change_reason)
                VALUES (%s, %s, %s, %s)
            ''', (user_uuid, self.project_id, new_level, reason))

            conn.commit()
            self.level = new_level
            return new_level

    @classmethod
    def get_current(cls, user_id: str) -> int:
        """获取当前好感度"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(user_id)
            if not user_uuid:
                return 10  # 默认初始好感度

            cursor.execute('''
                SELECT level FROM affection_levels
                WHERE user_id = %s AND project_id = %s
                ORDER BY timestamp DESC LIMIT 1
            ''', (user_uuid, PROJECT_ID))

            result = cursor.fetchone()
            return result[0] if result else 10  # 默认初始好感度


class PersonaState:
    """人格状态模型类"""

    def __init__(self, date: str = None, current_activity: str = None,
                 mood: str = None, work_content: str = None):
        self.date = date
        self.current_activity = current_activity
        self.mood = mood
        self.work_content = work_content
        self.project_id = PROJECT_ID

    def save(self):
        """保存人格状态"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO persona_states (project_id, date, current_activity, mood, work_content)
                VALUES (%s, %s, %s, %s, %s)
            ''', (self.project_id, self.date, self.current_activity, self.mood, self.work_content))

            conn.commit()

    @classmethod
    def get_by_date(cls, date: str) -> Optional['PersonaState']:
        """根据日期获取人格状态"""
        with get_db_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            cursor.execute('''
                SELECT * FROM persona_states
                WHERE project_id = %s AND date = %s
            ''', (PROJECT_ID, date))

            result = cursor.fetchone()
            if result:
                data = dict(result)
                return cls(
                    date=str(data['date']),
                    current_activity=data['current_activity'],
                    mood=data['mood'],
                    work_content=data['work_content']
                )
            return None

    @classmethod
    def get_recent(cls, limit: int = 7) -> List[Dict]:
        """获取最近的人格状态"""
        with get_db_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            cursor.execute('''
                SELECT * FROM persona_states
                WHERE project_id = %s
                ORDER BY date DESC LIMIT %s
            ''', (PROJECT_ID, limit))

            return [dict(row) for row in cursor.fetchall()]


class UserEvent:
    """用户事件模型类"""

    def __init__(self, user_id: str = None, event_data: Dict = None,
                 embedding: List[float] = None):
        self.user_id = user_id
        self.event_data = event_data or {}
        self.embedding = embedding
        self.project_id = PROJECT_ID

    def save(self) -> str:
        """保存用户事件"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(self.user_id)
            if not user_uuid:
                raise ValueError(f"用户 {self.user_id} 不存在")

            cursor.execute('''
                INSERT INTO user_events (id, user_id, project_id, event_data, embedding)
                VALUES (gen_random_uuid(), %s, %s, %s, %s)
                RETURNING id
            ''', (user_uuid, self.project_id, json.dumps(self.event_data), self.embedding))

            event_id = cursor.fetchone()[0]
            conn.commit()
            return str(event_id)

    @classmethod
    def get_user_events(cls, user_id: str, limit: int = 10) -> List[Dict]:
        """获取用户事件"""
        with get_db_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(user_id)
            if not user_uuid:
                return []

            cursor.execute('''
                SELECT * FROM user_events
                WHERE user_id = %s AND project_id = %s
                ORDER BY created_at DESC LIMIT %s
            ''', (user_uuid, PROJECT_ID, limit))

            return [dict(row) for row in cursor.fetchall()]


class UserAuth:
    """用户认证模型"""

    def __init__(self, username: str, password_hash: str = None, email: str = None):
        self.username = username
        self.password_hash = password_hash
        self.email = email
        self.user_id = None
        self.status = 'active'
        self.created_at = None
        self.updated_at = None

    def save(self) -> str:
        """保存用户认证信息，返回用户ID"""
        import uuid

        if not self.user_id:
            self.user_id = str(uuid.uuid4())

        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO user_auth (user_id, project_id, username, password_hash, email, status)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (username) DO UPDATE SET
                    password_hash = EXCLUDED.password_hash,
                    email = EXCLUDED.email,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING user_id
            ''', (self.user_id, PROJECT_ID, self.username, self.password_hash, self.email, self.status))

            result = cursor.fetchone()
            self.user_id = str(result[0])
            conn.commit()

        return self.user_id

    @classmethod
    def find_by_username(cls, username: str) -> Optional['UserAuth']:
        """根据用户名查找用户"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT user_id, username, password_hash, email, status, created_at, updated_at
                FROM user_auth
                WHERE username = %s AND project_id = %s
            ''', (username, PROJECT_ID))

            result = cursor.fetchone()
            if not result:
                return None

            user_id, username, password_hash, email, status, created_at, updated_at = result

            user_auth = cls(username, password_hash, email)
            user_auth.user_id = str(user_id)
            user_auth.status = status
            user_auth.created_at = created_at
            user_auth.updated_at = updated_at

            return user_auth

    @classmethod
    def username_exists(cls, username: str) -> bool:
        """检查用户名是否已存在"""
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT 1 FROM user_auth
                WHERE username = %s AND project_id = %s
            ''', (username, PROJECT_ID))
            return cursor.fetchone() is not None

    @classmethod
    def ensure_tables(cls):
        """确保用户认证表存在"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 创建用户认证表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_auth (
                    id SERIAL PRIMARY KEY,
                    user_id UUID NOT NULL,
                    project_id VARCHAR(64) NOT NULL DEFAULT %s,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password_hash TEXT,
                    email VARCHAR(100),
                    status VARCHAR(20) DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''', (PROJECT_ID,))

            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_auth_username ON user_auth(username)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_auth_user_id ON user_auth(user_id, project_id)')

            conn.commit()


class UserSession:
    """用户会话模型"""

    def __init__(self, user_id: str, session_token: str = None, expires_at: datetime = None):
        self.user_id = user_id
        self.session_token = session_token
        self.expires_at = expires_at
        self.is_active = True
        self.created_at = None

    def save(self) -> str:
        """保存会话信息，返回会话令牌"""
        import secrets
        from datetime import timedelta

        if not self.session_token:
            self.session_token = secrets.token_urlsafe(32)

        if not self.expires_at:
            self.expires_at = datetime.now() + timedelta(days=7)

        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO user_sessions (session_token, user_id, project_id, expires_at, is_active)
                VALUES (%s, %s, %s, %s, %s)
            ''', (self.session_token, self.user_id, PROJECT_ID, self.expires_at, self.is_active))

            conn.commit()

        return self.session_token

    @classmethod
    def find_by_token(cls, session_token: str) -> Optional['UserSession']:
        """根据会话令牌查找会话"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT s.user_id, s.session_token, s.expires_at, s.is_active, s.created_at,
                       u.username, u.email, u.status
                FROM user_sessions s
                JOIN user_auth u ON s.user_id = u.user_id AND s.project_id = u.project_id
                WHERE s.session_token = %s AND s.project_id = %s
            ''', (session_token, PROJECT_ID))

            result = cursor.fetchone()
            if not result:
                return None

            user_id, session_token, expires_at, is_active, created_at, username, email, status = result

            session = cls(str(user_id), session_token, expires_at)
            session.is_active = is_active
            session.created_at = created_at
            session.user_info = {
                'username': username,
                'email': email,
                'status': status
            }

            return session

    def is_valid(self) -> bool:
        """检查会话是否有效"""
        if not self.is_active:
            return False

        if datetime.now() > self.expires_at:
            return False

        if hasattr(self, 'user_info') and self.user_info.get('status') != 'active':
            return False

        return True

    def deactivate(self) -> bool:
        """停用会话"""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE user_sessions
                    SET is_active = FALSE
                    WHERE session_token = %s AND project_id = %s
                ''', (self.session_token, PROJECT_ID))

                conn.commit()
                result = cursor.rowcount > 0

                if result:
                    self.is_active = False

                return result
        except Exception:
            return False

    @classmethod
    def ensure_tables(cls):
        """确保用户会话表存在"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 创建用户会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id SERIAL PRIMARY KEY,
                    session_token VARCHAR(64) UNIQUE NOT NULL,
                    user_id UUID NOT NULL,
                    project_id VARCHAR(64) NOT NULL DEFAULT %s,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE
                )
            ''', (PROJECT_ID,))

            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id, project_id)')

            conn.commit()


# 初始化数据库（在模块加载时执行）
init_database()
