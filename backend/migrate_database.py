#!/usr/bin/env python3
"""
数据库迁移脚本
用于将现有数据库升级到支持用户管理的新结构
"""

import sqlite3
import os
import logging
from datetime import datetime
from config import Config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseMigrator:
    """数据库迁移器"""

    def __init__(self, db_path: str = None):
        self.db_path = db_path or Config.DATABASE_PATH

    def get_table_info(self, table_name: str) -> list:
        """获取表结构信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            return cursor.fetchall()

    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                (table_name,)
            )
            return cursor.fetchone() is not None

    def column_exists(self, table_name: str, column_name: str) -> bool:
        """检查列是否存在"""
        table_info = self.get_table_info(table_name)
        return any(col[1] == column_name for col in table_info)

    def backup_database(self):
        """备份数据库"""
        if not os.path.exists(self.db_path):
            logger.info("数据库文件不存在，无需备份")
            return

        backup_path = f"{self.db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        try:
            # 使用SQLite的备份API
            with sqlite3.connect(self.db_path) as source:
                with sqlite3.connect(backup_path) as backup:
                    source.backup(backup)

            logger.info(f"✅ 数据库备份成功: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"❌ 数据库备份失败: {e}")
            raise

    def migrate_users_table(self):
        """迁移用户表结构"""
        logger.info("🔄 开始迁移用户表...")

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 检查是否需要迁移
            if self.column_exists('users', 'username'):
                logger.info("⏭️  用户表已经是新结构")
                return

            logger.info("🔄 重建用户表结构...")

            # 1. 创建新的用户表
            cursor.execute('''
                CREATE TABLE users_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT UNIQUE NOT NULL,
                    username TEXT UNIQUE,
                    password_hash TEXT,
                    nickname TEXT,
                    email TEXT,
                    status TEXT DEFAULT 'active',
                    name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 2. 复制现有数据
            cursor.execute('''
                INSERT INTO users_new (id, user_id, name, created_at, last_active, nickname, status)
                SELECT id, user_id, name, created_at, last_active,
                       COALESCE(name, '用户' || substr(user_id, 1, 8)) as nickname,
                       'active' as status
                FROM users
            ''')

            # 3. 删除旧表
            cursor.execute('DROP TABLE users')

            # 4. 重命名新表
            cursor.execute('ALTER TABLE users_new RENAME TO users')

            conn.commit()

        logger.info("✅ 用户表迁移完成")

    def create_user_sessions_table(self):
        """创建用户会话表"""
        logger.info("🔄 创建用户会话表...")

        if self.table_exists('user_sessions'):
            logger.info("⏭️  用户会话表已存在")
            return

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            try:
                cursor.execute('''
                    CREATE TABLE user_sessions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_token TEXT UNIQUE NOT NULL,
                        user_id TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP NOT NULL,
                        is_active BOOLEAN DEFAULT 1,
                        FOREIGN KEY (user_id) REFERENCES users (user_id)
                    )
                ''')

                # 创建索引以提高查询性能
                cursor.execute('CREATE INDEX idx_sessions_token ON user_sessions(session_token)')
                cursor.execute('CREATE INDEX idx_sessions_user_id ON user_sessions(user_id)')
                cursor.execute('CREATE INDEX idx_sessions_expires ON user_sessions(expires_at)')

                conn.commit()
                logger.info("✅ 用户会话表创建成功")

            except sqlite3.Error as e:
                logger.error(f"❌ 创建用户会话表失败: {e}")
                raise

    def update_existing_users(self):
        """更新现有用户数据"""
        logger.info("🔄 更新现有用户数据...")

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 获取所有没有nickname的用户
            cursor.execute("SELECT user_id, name FROM users WHERE nickname IS NULL")
            users = cursor.fetchall()

            updated_count = 0
            for user_id, name in users:
                try:
                    # 使用name作为nickname，如果name为空则使用默认值
                    nickname = name if name else f"用户{user_id[:8]}"

                    cursor.execute(
                        "UPDATE users SET nickname = ?, status = ? WHERE user_id = ?",
                        (nickname, 'active', user_id)
                    )
                    updated_count += 1

                except sqlite3.Error as e:
                    logger.error(f"❌ 更新用户失败 {user_id}: {e}")

            conn.commit()
            logger.info(f"✅ 更新了 {updated_count} 个用户的数据")

    def create_indexes(self):
        """创建数据库索引以提高性能"""
        logger.info("🔄 创建数据库索引...")

        indexes = [
            ('idx_users_username', 'users', 'username'),
            ('idx_users_status', 'users', 'status'),
            ('idx_conversations_user_id', 'conversations', 'user_id'),
            ('idx_memories_user_id', 'memories', 'user_id'),
            ('idx_affection_user_id', 'affection_levels', 'user_id'),
        ]

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            for index_name, table_name, column_name in indexes:
                try:
                    cursor.execute(f'CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column_name})')
                    logger.info(f"✅ 创建索引: {index_name}")
                except sqlite3.Error as e:
                    logger.warning(f"⚠️  创建索引失败 {index_name}: {e}")

            conn.commit()

        logger.info("✅ 数据库索引创建完成")

    def verify_migration(self):
        """验证迁移结果"""
        logger.info("🔍 验证迁移结果...")

        issues = []

        # 检查用户表结构
        if not self.table_exists('users'):
            issues.append("用户表不存在")
        else:
            required_columns = ['username', 'password_hash', 'nickname', 'email', 'status']
            for column in required_columns:
                if not self.column_exists('users', column):
                    issues.append(f"用户表缺少列: {column}")

        # 检查会话表
        if not self.table_exists('user_sessions'):
            issues.append("用户会话表不存在")

        # 检查数据完整性
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 检查是否有用户没有nickname
            cursor.execute("SELECT COUNT(*) FROM users WHERE nickname IS NULL")
            null_nickname_count = cursor.fetchone()[0]
            if null_nickname_count > 0:
                issues.append(f"有 {null_nickname_count} 个用户没有昵称")

        if issues:
            logger.error("❌ 迁移验证失败:")
            for issue in issues:
                logger.error(f"   - {issue}")
            return False
        else:
            logger.info("✅ 迁移验证通过")
            return True

    def run_migration(self):
        """执行完整的数据库迁移"""
        logger.info("🚀 开始数据库迁移...")

        try:
            # 1. 备份数据库
            backup_path = self.backup_database()

            # 2. 迁移用户表
            self.migrate_users_table()

            # 3. 创建会话表
            self.create_user_sessions_table()

            # 4. 更新现有用户数据
            self.update_existing_users()

            # 5. 创建索引
            self.create_indexes()

            # 6. 验证迁移
            if self.verify_migration():
                logger.info("🎉 数据库迁移成功完成!")
                if backup_path:
                    logger.info(f"💾 备份文件: {backup_path}")
                return True
            else:
                logger.error("💥 数据库迁移验证失败")
                return False

        except Exception as e:
            logger.error(f"💥 数据库迁移失败: {e}")
            if backup_path:
                logger.info(f"💾 可以从备份恢复: {backup_path}")
            raise

def main():
    """主函数"""
    print("=" * 60)
    print("🗃️  虚拟人系统数据库迁移工具")
    print("=" * 60)

    migrator = DatabaseMigrator()

    # 检查数据库文件是否存在
    if not os.path.exists(migrator.db_path):
        print(f"📁 数据库文件不存在: {migrator.db_path}")
        print("🆕 将在首次运行时自动创建新数据库")
        return

    print(f"📁 数据库路径: {migrator.db_path}")

    # 自动执行迁移
    print("\n🚀 开始自动执行数据库迁移...")

    # 执行迁移
    try:
        success = migrator.run_migration()
        if success:
            print("\n🎉 迁移完成! 现在可以使用新的用户管理功能了。")
        else:
            print("\n💥 迁移失败! 请检查日志信息。")
    except Exception as e:
        print(f"\n💥 迁移过程中发生错误: {e}")

if __name__ == "__main__":
    main()
