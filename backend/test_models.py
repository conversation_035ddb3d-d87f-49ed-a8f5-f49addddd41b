#!/usr/bin/env python3
"""
测试核心模型类
"""

from models.memory import User, Conversation, AffectionLevel, PersonaState, UserEvent

def test_all_models():
    """测试所有模型类"""
    print("🧪 开始测试核心模型类...")
    
    print("✅ 数据库连接成功")

    # 测试用户模型
    print("\n📝 测试用户模型...")
    user = User.create('test_user_model', '测试用户模型')
    print(f"✅ 用户创建成功: {user.user_id}")

    user_uuid = user.get_uuid()
    print(f"✅ 获取用户UUID成功: {user_uuid}")

    # 测试对话模型
    print("\n💬 测试对话模型...")
    conversation = Conversation('test_user_model', '你好，这是测试消息', 'user', 0.8)
    conversation.save()
    print("✅ 对话保存成功")

    recent_conversations = Conversation.get_recent('test_user_model', 5)
    print(f"✅ 获取最近对话成功: {len(recent_conversations)} 条")

    # 测试好感度模型
    print("\n❤️ 测试好感度模型...")
    affection = AffectionLevel('test_user_model')
    new_level = affection.update(10, '测试好感度更新')
    print(f"✅ 好感度更新成功: {new_level}")

    current_level = AffectionLevel.get_current('test_user_model')
    print(f"✅ 获取当前好感度成功: {current_level}")

    # 测试人格状态模型
    print("\n🎭 测试人格状态模型...")
    persona = PersonaState('2025-06-07', '学习编程', '开心', '重构代码模型')
    persona.save()
    print("✅ 人格状态保存成功")

    persona_by_date = PersonaState.get_by_date('2025-06-07')
    if persona_by_date:
        print(f"✅ 根据日期获取人格状态成功: {persona_by_date.mood}")

    recent_personas = PersonaState.get_recent(3)
    print(f"✅ 获取最近人格状态成功: {len(recent_personas)} 条")

    # 测试用户事件模型
    print("\n📅 测试用户事件模型...")
    event = UserEvent('test_user_model', {'action': 'test', 'content': '测试事件'})
    event_id = event.save()
    print(f"✅ 用户事件保存成功: {event_id}")

    user_events = UserEvent.get_user_events('test_user_model', 5)
    print(f"✅ 获取用户事件成功: {len(user_events)} 条")

    # 测试直接调用函数
    print("\n🔄 测试直接函数调用...")
    test_conversation = Conversation('test_user_model', '直接函数测试消息', 'assistant', 0.9)
    test_conversation.save()
    print("✅ 直接函数调用 save_conversation 成功")

    conversations = Conversation.get_recent('test_user_model', 3)
    print(f"✅ 直接函数调用 get_recent_conversations 成功: {len(conversations)} 条")

    test_affection = AffectionLevel('test_user_model')
    new_affection = test_affection.update(5, '直接函数测试')
    print(f"✅ 直接函数调用 update_affection 成功: {new_affection}")

    current_affection = AffectionLevel.get_current('test_user_model')
    print(f"✅ 直接函数调用 get_current_affection 成功: {current_affection}")
    
    print("\n🎉 所有模型测试通过！")
    print("=" * 50)
    print("📊 测试总结:")
    print("✅ User 模型 - 创建、获取UUID")
    print("✅ Conversation 模型 - 保存、获取最近对话")
    print("✅ AffectionLevel 模型 - 更新、获取当前好感度")
    print("✅ PersonaState 模型 - 保存、按日期获取、获取最近状态")
    print("✅ UserEvent 模型 - 保存、获取用户事件")
    print("✅ 直接函数调用 - 所有核心功能正常工作")

if __name__ == '__main__':
    test_all_models()
