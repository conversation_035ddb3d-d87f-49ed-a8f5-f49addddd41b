请分析以下用户文本，提取用户画像信息。

用户文本：
{{ text }}

请按照以下JSON格式返回分析结果：
{
    "profile_deltas": [
        {
            "content": "提取的画像内容",
            "attributes": {
                "topic": "从以下选择: {{ topics_str }}",
                "sub_topic": "具体的子话题",
                "confidence": 0.8
            }
        }
    ],
    "event_tip": "对这次对话的简要总结",
    "event_tags": [
        {
            "tag": "从以下选择: {{ tags_str }}",
            "value": "标签的具体值"
        }
    ]
}

要求：
1. 只提取明确的、有价值的信息
2. confidence范围0.1-1.0，表示提取的置信度
3. 每个profile_delta应该是独立的信息点
4. 如果没有明确信息，返回空数组
5. 必须返回有效的JSON格式