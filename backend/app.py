from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import sys
from datetime import datetime

# 设置UTF-8编码
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8')

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 配置日志
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(pathname)s:%(lineno)d - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

from services.conversation_engine import ConversationEngine
from services.chat_history_service import ChatHistoryService
from services.user_manager import get_user_manager
from models.memory import Conversation


app = Flask(__name__)
CORS(app)  # 允许跨域请求



# 初始化服务
conversation_engine = ConversationEngine()
chat_history_service = ChatHistoryService()
user_manager = get_user_manager()

@app.route('/')
def index():
    """提供前端页面"""
    return send_from_directory('../frontend', 'index.html')

@app.route('/admin')
def admin_redirect():
    """重定向到新的管理界面"""
    return send_from_directory('../frontend/admin', 'index.html')

@app.route('/admin/')
def admin_index():
    """提供管理界面"""
    return send_from_directory('../frontend/admin', 'index.html')



@app.route('/admin/<path:filename>')
def admin_static_files(filename):
    """提供管理界面静态文件"""
    return send_from_directory('../frontend/admin', filename)

@app.route('/<path:filename>')
def static_files(filename):
    """提供静态文件"""
    return send_from_directory('../frontend', filename)

@app.route('/api/chat', methods=['POST'])
def chat():
    """处理聊天消息"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        data = request.get_json()

        if not data or 'message' not in data:
            return jsonify({'error': '消息内容不能为空'}), 400

        message = data['message'].strip()

        if not message:
            return jsonify({'error': '消息内容不能为空'}), 400

        # 使用会话中的用户ID
        user_id = user['user_id']

        # 处理消息
        result = conversation_engine.process_message(user_id, message)

        return jsonify({
            'success': True,
            'user_id': user_id,
            'response': result['response'],
            'response_segments': result.get('response_segments', [result['response']]),
            'affection_level': result['affection_level'],
            'affection_change': result['affection_change'],
            'emotion': result['emotion'],
            'memories_extracted': result['memories_extracted'],
            'persona_activity': result['persona_activity'],
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"Chat error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误，请稍后重试'
        }), 500

@app.route('/api/greeting', methods=['POST'])
def greeting():
    """获取问候消息"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        # 使用会话中的用户ID
        user_id = user['user_id']

        result = conversation_engine.get_greeting_message(user_id)

        return jsonify({
            'success': True,
            'user_id': user_id,
            'response': result['response'],
            'affection_level': result['affection_level'],
            'persona_activity': result['persona_activity'],
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"Greeting error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误，请稍后重试'
        }), 500
@app.route('/api/chat/voice', methods=['POST'])
def chat_voice():
    """处理语音聊天消息"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        # 检查是否有语音文件
        if 'voice' not in request.files:
            return jsonify({'error': '未找到语音文件'}), 400

        voice_file = request.files['voice']
        if voice_file.filename == '':
            return jsonify({'error': '语音文件为空'}), 400

        # 这里应该实现语音转文字的逻辑
        # 暂时返回模拟响应
        user_id = user['user_id']

        # 模拟语音转文字结果
        transcribed_text = "这是语音转换的文字内容"

        # 使用现有的消息处理逻辑
        result = conversation_engine.process_message(user_id, transcribed_text)

        return jsonify({
            'success': True,
            'user_id': user_id,
            'transcribed_text': transcribed_text,
            'response': result['response'],
            'segments': result.get('response_segments', [result['response']]),
            'affection_level': result['affection_level'],
            'affection_change': result['affection_change'],
            'emotion': result['emotion'],
            'memories_extracted': result['memories_extracted'],
            'persona_activity': result['persona_activity'],
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"Voice chat error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '语音处理失败，请稍后重试'
        }), 500
@app.route('/api/chat/history', methods=['GET'])
def get_chat_history():
    """获取聊天历史记录"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        user_id = user['user_id']

        # 获取分页参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        before_timestamp = request.args.get('before_timestamp')  # 用于上滑加载

        # 计算偏移量
        offset = (page - 1) * page_size

        # 获取历史对话
        conversations = chat_history_service.get_chat_history_with_pagination(
            user_id,
            limit=page_size,
            offset=offset,
            before_timestamp=before_timestamp
        )

        # 检查是否还有更多数据
        has_more = len(conversations) == page_size

        return jsonify({
            'success': True,
            'conversations': conversations,
            'has_more': has_more,
            'page': page,
            'page_size': page_size
        })

    except Exception as e:
        print(f"Get chat history error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取聊天历史失败'
        }), 500

















# 用户管理API
@app.route('/api/users/register', methods=['POST'])
def register_user():
    """用户注册"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        nickname = data.get('nickname', '').strip()
        email = data.get('email', '').strip()

        if not username:
            return jsonify({'error': '用户名不能为空'}), 400

        if not password:
            return jsonify({'error': '密码不能为空'}), 400

        # 创建用户
        user = user_manager.create_user(
            username=username,
            password=password,
            nickname=nickname if nickname else None,
            email=email if email else None
        )

        return jsonify({
            'success': True,
            'message': '用户注册成功',
            'user': user
        })

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        print(f"Register error: {str(e)}")
        return jsonify({'error': '注册失败，请稍后重试'}), 500

@app.route('/api/users/login', methods=['POST'])
def login_user():
    """用户登录"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        username = data.get('username', '').strip()
        password = data.get('password', '').strip()

        if not username or not password:
            return jsonify({'error': '用户名和密码不能为空'}), 400

        # 用户认证
        user = user_manager.authenticate_user(username, password)
        if not user:
            return jsonify({'error': '用户名或密码错误'}), 401

        # 创建会话
        session_token = user_manager.create_session(user['user_id'])

        return jsonify({
            'success': True,
            'message': '登录成功',
            'user': user,
            'session_token': session_token
        })

    except Exception as e:
        print(f"Login error: {str(e)}")
        return jsonify({'error': '登录失败，请稍后重试'}), 500

@app.route('/api/users/logout', methods=['POST'])
def logout_user():
    """用户登出"""
    try:
        session_token = request.headers.get('Authorization')
        if session_token and session_token.startswith('Bearer '):
            session_token = session_token[7:]  # 移除 "Bearer " 前缀

        if not session_token:
            return jsonify({'error': '未找到会话令牌'}), 400

        # 登出用户
        success = user_manager.logout_user(session_token)

        if success:
            return jsonify({
                'success': True,
                'message': '登出成功'
            })
        else:
            return jsonify({'error': '登出失败'}), 400

    except Exception as e:
        print(f"Logout error: {str(e)}")
        return jsonify({'error': '登出失败，请稍后重试'}), 500





def validate_session():
    """验证会话中间件"""
    session_token = request.headers.get('Authorization')
    if session_token and session_token.startswith('Bearer '):
        session_token = session_token[7:]  # 移除 "Bearer " 前缀

    if not session_token:
        # 如果没有会话令牌，返回默认用户（用于兼容现有功能）
        return {
            'user_id': 'default_user',
            'username': 'default_user',
            'nickname': '默认用户'
        }

    # 验证会话
    user = user_manager.validate_session(session_token)
    if user:
        return user

    # 会话无效，返回默认用户
    return {
        'user_id': 'default_user',
        'username': 'default_user',
        'nickname': '默认用户'
    }

# 管理API
@app.route('/api/admin/stats', methods=['GET'])
def get_admin_stats():
    """获取管理统计数据 - 暂时简化"""
    try:
        # 获取记忆统计
        memory_stats = conversation_engine.memory_manager.get_memory_statistics()
        total_memories = memory_stats.get('total', 0)

        return jsonify({
            'success': True,
            'total_users': 1,  # 暂时硬编码
            'total_conversations': 0,  # 暂时硬编码
            'total_memories': total_memories,
            'avg_affection': 50  # 暂时硬编码
        })
    except Exception as e:
        print(f"Get admin stats error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/admin/users', methods=['GET'])
def get_admin_users():
    """获取所有用户列表 - 暂时简化"""
    try:
        # 暂时返回默认用户
        users = [{
            'user_id': 'default_user',
            'username': 'default_user',
            'nickname': '默认用户',
            'affection_level': 50,
            'last_active': '2024-01-01T00:00:00',
            'memobase_id': '未映射'
        }]
        return jsonify(users)
    except Exception as e:
        print(f"Get admin users error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/admin/activity', methods=['GET'])
def get_admin_activity():
    """获取最近活动"""
    try:
        # 这里可以实现获取最近活动的逻辑
        # 暂时返回模拟数据
        activities = [
            {
                'title': '新用户注册',
                'description': '用户 test_user 完成注册',
                'timestamp': datetime.now().isoformat()
            },
            {
                'title': '对话记录',
                'description': '用户与沈沐心进行了对话',
                'timestamp': datetime.now().isoformat()
            }
        ]

        return jsonify(activities)
    except Exception as e:
        print(f"Get admin activity error: {str(e)}")
        return jsonify([])

@app.route('/api/admin/config/persona', methods=['GET'])
def get_persona_config():
    """获取人设配置"""
    try:
        # 这里可以返回人设配置信息
        config = {
            'name': '沈沐心',
            'age': '25岁',
            'profession': '心理咨询师',
            'personality': '温柔、善解人意、专业、有耐心'
        }

        return jsonify(config)
    except Exception as e:
        print(f"Get persona config error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500





@app.route('/api/admin/memories/stats', methods=['GET'])
def get_admin_memory_stats():
    """管理界面获取记忆统计"""
    try:
        # 获取用户记忆统计
        user_stats = conversation_engine.memory_manager.get_memory_statistics()

        # 获取个人记忆统计
        persona_stats = conversation_engine.persona_memory_manager.get_memory_stats()

        # 获取活跃用户数 - 暂时硬编码
        active_users = 1

        return jsonify({
            'success': True,
            'user_memories': user_stats,
            'persona_memories': persona_stats,
            'active_users': active_users
        })
    except Exception as e:
        print(f"Get admin memory stats error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500



# ==================== Memobase代理转发 ====================

@app.route('/api/memobase/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
def memobase_proxy(path):
    """Memobase API代理转发"""
    try:
        import requests
        from config import Config

        memobase_config = Config.MEMOBASE_CONFIG
        base_url = memobase_config.get('project_url', 'http://localhost:8019')
        api_key = memobase_config.get('api_key', 'secret')

        # 构建目标URL
        target_url = f"{base_url}/api/{path}"

        # 准备请求头
        headers = dict(request.headers)
        # 移除可能冲突的头部
        headers.pop('Host', None)
        headers.pop('Content-Length', None)

        # 添加认证头 - Memobase使用Bearer token认证
        if api_key:
            headers['Authorization'] = f'Bearer {api_key}'

        # 准备请求参数
        params = dict(request.args)

        # 准备请求体
        data = None
        json_data = None
        if request.is_json:
            json_data = request.get_json()
        elif request.data:
            data = request.data

        # 发送请求
        response = requests.request(
            method=request.method,
            url=target_url,
            headers=headers,
            params=params,
            data=data,
            json=json_data,
            timeout=30
        )

        # 返回响应
        return response.content, response.status_code, dict(response.headers)

    except Exception as e:
        print(f"Memobase proxy error: {str(e)}")
        return jsonify({
            'error': f'代理转发失败: {str(e)}'
        }), 500



if __name__ == '__main__':
    print("启动虚拟人恋爱陪伴系统...")
    print("前端地址: http://localhost:8080")
    print("API地址: http://localhost:8080/api")

    app.run(
        host='0.0.0.0',
        port=8080,
        debug=True
    )
