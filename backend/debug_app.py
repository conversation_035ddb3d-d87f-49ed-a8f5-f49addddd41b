#!/usr/bin/env python3
"""
调试启动脚本
专门用于断点调试的Flask应用启动文件
"""

import os
import sys

# 设置调试环境
os.environ['FLASK_ENV'] = 'development'
os.environ['FLASK_DEBUG'] = '1'
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入Flask应用
from app import app

if __name__ == '__main__':
    print("🐛 启动调试模式...")
    print("=" * 50)
    print("📍 断点调试已启用")
    print("🌐 访问地址: http://localhost:8080")
    print("🛑 按 Ctrl+C 停止调试")
    print("=" * 50)
    
    # 启动Flask应用（调试模式）
    app.run(
        host='0.0.0.0',
        port=8080,
        debug=True,
        use_reloader=False,  # 禁用自动重载，避免调试器冲突
        use_debugger=False   # 禁用Flask内置调试器，使用VSCode调试器
    )
