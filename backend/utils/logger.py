"""
日志配置工具 - 统一处理中文编码问题
"""

import logging
import sys
import os
from datetime import datetime

def setup_logger(name: str = None) -> logging.Logger:
    """
    设置日志记录器，确保中文正确显示

    Args:
        name: 日志记录器名称

    Returns:
        配置好的日志记录器
    """

    # 确保控制台输出使用UTF-8编码
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8')
    if hasattr(sys.stderr, 'reconfigure'):
        sys.stderr.reconfigure(encoding='utf-8')

    # 设置环境变量确保UTF-8编码
    os.environ['PYTHONIOENCODING'] = 'utf-8'

    # 创建日志记录器
    logger = logging.getLogger(name or __name__)

    # 避免重复添加处理器
    if logger.handlers:
        return logger

    logger.setLevel(logging.INFO)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 创建格式化器 - 包含文件路径和行号
    formatter = logging.Formatter(
        '%(asctime)s - %(pathname)s:%(lineno)d - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_handler.setFormatter(formatter)

    # 添加处理器到日志记录器
    logger.addHandler(console_handler)

    return logger

def log_llm_request(logger: logging.Logger, request_id: str, user_message: str,
                   system_prompt: str, conversation_history: list = None, **kwargs):
    """
    记录LLM请求日志
    """
    logger.info(f"🚀 LLM请求 [{request_id}]")
    logger.info(f"📝 用户消息: {user_message}")
    logger.info(f"🎭 系统提示词: {system_prompt[:200]}...")
    logger.info(f"📚 对话历史: {len(conversation_history) if conversation_history else 0} 条")

    for key, value in kwargs.items():
        logger.info(f"⚙️  {key}: {value}")

def log_llm_response(logger: logging.Logger, request_id: str, response: str, **kwargs):
    """
    记录LLM响应日志
    """
    logger.info(f"✅ LLM响应成功 [{request_id}]")
    logger.info(f"💬 生成回复: {response}")
    logger.info(f"📊 响应统计: {len(response)} 字符")

    for key, value in kwargs.items():
        logger.info(f"📈 {key}: {value}")

def log_emotion_analysis(logger: logging.Logger, request_id: str, text: str, result: dict):
    """
    记录情感分析日志
    """
    logger.info(f"🎭 情感分析请求 [{request_id}]")
    logger.info(f"📝 分析文本: {text}")
    logger.info(f"✅ 情感分析成功")
    logger.info(f"😊 情感结果: {result}")

def log_memory_extraction(logger: logging.Logger, request_id: str, text: str, memories: list):
    """
    记录记忆提取日志
    """
    logger.info(f"🧠 记忆提取请求 [{request_id}]")
    logger.info(f"📝 提取文本: {text}")
    logger.info(f"✅ 记忆提取成功")
    logger.info(f"🧠 提取记忆: {len(memories)} 条")

    for i, memory in enumerate(memories):
        logger.info(f"   {i+1}. [{memory.get('type', 'unknown')}] {memory.get('content', '')}")

def log_error(logger: logging.Logger, request_id: str, error_type: str, error_msg: str):
    """
    记录错误日志
    """
    logger.error(f"❌ {error_type} [{request_id}]")
    logger.error(f"错误信息: {error_msg}")

def log_fallback(logger: logging.Logger, request_id: str, fallback_response: str):
    """
    记录备用回复日志
    """
    logger.info(f"🔄 使用备用回复 [{request_id}]: {fallback_response}")
