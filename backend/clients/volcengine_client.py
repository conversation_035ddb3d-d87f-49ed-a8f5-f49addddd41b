"""
火山引擎客户端 - 统一的火山引擎API调用接口
支持Chat Completions和Embeddings API
"""

from config import Config
import logging
from datetime import datetime
import requests
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class VolcengineClient:
    """火山引擎客户端 - 支持Chat Completions和Embeddings API"""

    def __init__(self, access_key: str, secret_key: str, region: str = "cn-beijing"):
        self.access_key = access_key
        self.secret_key = secret_key
        self.region = region
        self.base_url = "https://ark.cn-beijing.volces.com/api/v3"
        logger.info(f"✅ 火山引擎客户端初始化成功，区域: {region}")
    
    def chat_completions(
        self,
        messages: List[Dict[str, str]],
        model: str = "doubao-lite-4k",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        **kwargs
    ) -> Dict[str, Any]:
        """
        调用火山引擎Chat Completions API
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        
        try:
            # 构建请求数据
            data = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False,
                **kwargs
            }
            
            # 记录请求日志
            logger.info(f"🌐 火山引擎API请求 [{request_id}]")
            logger.info(f"📋 请求数据: model={model}, messages={len(messages)}条, temperature={temperature}")
            logger.info(f"💬 最后一条消息: {messages[-1]['content'][:100]}..." if messages else "无消息")
            
            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self._get_access_token()}"
            }
            
            # 发送请求
            logger.info(f"🚀 发送请求到: {self.base_url}/chat/completions")
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            # 处理响应
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                
                logger.info(f"✅ 火山引擎API响应成功 [{request_id}]")
                logger.info(f"📝 响应内容: {content}")
                logger.info(f"📊 响应统计: {len(content)} 字符")
                
                return {
                    "success": True,
                    "content": content,
                    "raw_response": result,
                    "request_id": request_id
                }
            else:
                logger.error(f"❌ 火山引擎API错误 [{request_id}]")
                logger.error(f"状态码: {response.status_code}")
                logger.error(f"错误信息: {response.text}")
                
                return {
                    "success": False,
                    "error": f"API错误: {response.status_code}",
                    "error_detail": response.text,
                    "request_id": request_id
                }
                
        except requests.exceptions.Timeout:
            logger.error(f"⏰ 火山引擎API超时 [{request_id}]")
            return {
                "success": False,
                "error": "请求超时",
                "request_id": request_id
            }
        except requests.exceptions.ConnectionError:
            logger.error(f"🔌 火山引擎API连接错误 [{request_id}]")
            return {
                "success": False,
                "error": "连接错误",
                "request_id": request_id
            }
        except Exception as e:
            logger.error(f"💥 火山引擎API异常 [{request_id}]: {str(e)}")
            return {
                "success": False,
                "error": f"未知错误: {str(e)}",
                "request_id": request_id
            }
    
    def embeddings(
        self,
        input_texts: List[str],
        model: str = "doubao-embedding-text-240715",
        **kwargs
    ) -> Dict[str, Any]:
        """
        调用火山引擎Embeddings API

        Args:
            input_texts: 输入文本列表
            model: embedding模型名称
            **kwargs: 其他参数

        Returns:
            API响应结果
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")

        try:
            # 构建请求数据
            data = {
                "model": model,
                "input": input_texts,
                **kwargs
            }

            # 记录请求日志
            logger.info(f"🌐 火山引擎Embedding API请求 [{request_id}]")
            logger.info(f"📋 请求数据: model={model}, 文本数量={len(input_texts)}")
            logger.info(f"💬 第一条文本: {input_texts[0][:100]}..." if input_texts else "无文本")

            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self._get_access_token()}"
            }

            # 发送请求
            logger.info(f"🚀 发送Embedding请求到: {self.base_url}/embeddings")
            response = requests.post(
                f"{self.base_url}/embeddings",
                headers=headers,
                json=data,
                timeout=30
            )

            # 处理响应
            if response.status_code == 200:
                result = response.json()
                embeddings_data = result.get('data', [])

                logger.info(f"✅ 火山引擎Embedding API响应成功 [{request_id}]")
                logger.info(f"📊 返回embedding数量: {len(embeddings_data)}")
                if embeddings_data:
                    logger.info(f"📏 embedding维度: {len(embeddings_data[0].get('embedding', []))}")

                return {
                    "success": True,
                    "data": embeddings_data,
                    "raw_response": result,
                    "request_id": request_id
                }
            else:
                logger.error(f"❌ 火山引擎Embedding API错误 [{request_id}]")
                logger.error(f"状态码: {response.status_code}")
                logger.error(f"错误信息: {response.text}")

                return {
                    "success": False,
                    "error": f"API错误: {response.status_code}",
                    "error_detail": response.text,
                    "request_id": request_id
                }

        except requests.exceptions.Timeout:
            logger.error(f"⏰ 火山引擎Embedding API超时 [{request_id}]")
            return {
                "success": False,
                "error": "请求超时",
                "request_id": request_id
            }
        except requests.exceptions.ConnectionError:
            logger.error(f"🔌 火山引擎Embedding API连接错误 [{request_id}]")
            return {
                "success": False,
                "error": "连接错误",
                "request_id": request_id
            }
        except Exception as e:
            logger.error(f"💥 火山引擎Embedding API异常 [{request_id}]: {str(e)}")
            return {
                "success": False,
                "error": f"未知错误: {str(e)}",
                "request_id": request_id
            }

    def _get_access_token(self) -> str:
        """
        获取访问令牌
        注意：这里需要根据火山引擎的实际认证方式进行调整
        """
        return self.access_key


def create_volcengine_client(access_key: str, secret_key: str, region: str = "cn-beijing") -> VolcengineClient:
    """
    创建火山引擎客户端

    Args:
        access_key: 访问密钥
        secret_key: 密钥
        region: 区域

    Returns:
        火山引擎客户端实例
    """
    return VolcengineClient(
        access_key=access_key,
        secret_key=secret_key,
        region=region
    )

def get_volcengine_client() -> VolcengineClient:
    """
    获取火山引擎客户端实例
    
    Returns:
        火山引擎客户端实例
    """
    return create_volcengine_client(
        access_key=Config.VOLCENGINE_ACCESS_KEY,
        secret_key=Config.VOLCENGINE_SECRET_KEY,
        region=Config.VOLCENGINE_REGION
    )
