#!/usr/bin/env python3
"""
数据迁移脚本
将SQLite数据迁移到PostgreSQL
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.memory import get_memory_db
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DataMigrator:
    """数据迁移器"""
    
    def __init__(self):
        self.sqlite_path = Config.DATABASE_PATH
        self.pg_db = get_memory_db()
        
    def check_sqlite_database(self) -> bool:
        """检查SQLite数据库是否存在"""
        if not os.path.exists(self.sqlite_path):
            logger.warning(f"SQLite数据库不存在: {self.sqlite_path}")
            return False
        
        logger.info(f"✅ 找到SQLite数据库: {self.sqlite_path}")
        return True
    
    def get_sqlite_connection(self):
        """获取SQLite连接"""
        return sqlite3.connect(self.sqlite_path)
    
    def migrate_users(self) -> int:
        """迁移用户数据"""
        logger.info("🔄 开始迁移用户数据...")
        
        migrated_count = 0
        
        with self.get_sqlite_connection() as sqlite_conn:
            sqlite_cursor = sqlite_conn.cursor()
            
            # 获取SQLite中的用户数据
            sqlite_cursor.execute('''
                SELECT user_id, name, nickname, email, status, 
                       memobase_uuid, created_at, last_active
                FROM users
            ''')
            
            users = sqlite_cursor.fetchall()
            
            for user in users:
                try:
                    user_id, name, nickname, email, status, memobase_uuid, created_at, last_active = user
                    
                    # 构造额外数据
                    additional_data = {
                        'nickname': nickname,
                        'email': email,
                        'status': status or 'active',
                        'memobase_uuid': memobase_uuid,
                        'last_active': last_active,
                        'migrated_from': 'sqlite',
                        'migration_time': datetime.now().isoformat()
                    }
                    
                    # 创建PostgreSQL用户
                    self.pg_db.create_user(
                        user_id=user_id,
                        name=name or nickname or f'用户{user_id[:8]}',
                        additional_data=additional_data
                    )
                    
                    migrated_count += 1
                    logger.info(f"✅ 迁移用户: {user_id} ({name or nickname})")
                    
                except Exception as e:
                    logger.error(f"❌ 迁移用户失败 {user_id}: {e}")
        
        logger.info(f"🎉 用户迁移完成: {migrated_count} 个用户")
        return migrated_count
    
    def migrate_conversations(self) -> int:
        """迁移对话记录"""
        logger.info("🔄 开始迁移对话记录...")
        
        migrated_count = 0
        
        with self.get_sqlite_connection() as sqlite_conn:
            sqlite_cursor = sqlite_conn.cursor()
            
            # 获取SQLite中的对话数据
            sqlite_cursor.execute('''
                SELECT user_id, message, sender, timestamp, emotion_score
                FROM conversations
                ORDER BY timestamp
            ''')
            
            conversations = sqlite_cursor.fetchall()
            
            for conv in conversations:
                try:
                    user_id, message, sender, timestamp, emotion_score = conv
                    
                    # 保存到PostgreSQL
                    self.pg_db.save_conversation(
                        user_id=user_id,
                        message=message,
                        sender=sender,
                        emotion_score=emotion_score or 0.0
                    )
                    
                    migrated_count += 1
                    
                    if migrated_count % 100 == 0:
                        logger.info(f"📝 已迁移 {migrated_count} 条对话记录...")
                    
                except Exception as e:
                    logger.error(f"❌ 迁移对话失败: {e}")
        
        logger.info(f"🎉 对话记录迁移完成: {migrated_count} 条记录")
        return migrated_count
    
    def migrate_affection_levels(self) -> int:
        """迁移好感度记录"""
        logger.info("🔄 开始迁移好感度记录...")
        
        migrated_count = 0
        
        with self.get_sqlite_connection() as sqlite_conn:
            sqlite_cursor = sqlite_conn.cursor()
            
            # 获取SQLite中的好感度数据
            sqlite_cursor.execute('''
                SELECT user_id, level, change_reason, timestamp
                FROM affection_levels
                ORDER BY timestamp
            ''')
            
            affections = sqlite_cursor.fetchall()
            
            # 按用户分组处理
            user_affections = {}
            for aff in affections:
                user_id, level, change_reason, timestamp = aff
                if user_id not in user_affections:
                    user_affections[user_id] = []
                user_affections[user_id].append((level, change_reason, timestamp))
            
            # 为每个用户迁移好感度记录
            for user_id, records in user_affections.items():
                try:
                    # 只迁移最新的好感度记录
                    latest_record = records[-1]
                    level, change_reason, timestamp = latest_record
                    
                    with self.pg_db.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute('''
                            INSERT INTO affection_levels (user_id, level, change_reason, timestamp)
                            VALUES (%s, %s, %s, %s)
                        ''', (user_id, level, change_reason or '数据迁移', timestamp))
                        conn.commit()
                    
                    migrated_count += 1
                    logger.info(f"💖 迁移好感度: {user_id} -> {level}")
                    
                except Exception as e:
                    logger.error(f"❌ 迁移好感度失败 {user_id}: {e}")
        
        logger.info(f"🎉 好感度记录迁移完成: {migrated_count} 条记录")
        return migrated_count
    
    def migrate_persona_states(self) -> int:
        """迁移虚拟人状态"""
        logger.info("🔄 开始迁移虚拟人状态...")
        
        migrated_count = 0
        
        with self.get_sqlite_connection() as sqlite_conn:
            sqlite_cursor = sqlite_conn.cursor()
            
            # 检查表是否存在
            sqlite_cursor.execute('''
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='persona_states'
            ''')
            
            if not sqlite_cursor.fetchone():
                logger.info("📝 SQLite中没有persona_states表，跳过迁移")
                return 0
            
            # 获取SQLite中的虚拟人状态数据
            sqlite_cursor.execute('''
                SELECT date, current_activity, mood, work_content, created_at
                FROM persona_states
                ORDER BY created_at
            ''')
            
            states = sqlite_cursor.fetchall()
            
            for state in states:
                try:
                    date, current_activity, mood, work_content, created_at = state
                    
                    with self.pg_db.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute('''
                            INSERT INTO persona_states (date, current_activity, mood, work_content, created_at)
                            VALUES (%s, %s, %s, %s, %s)
                        ''', (date, current_activity, mood, work_content, created_at))
                        conn.commit()
                    
                    migrated_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ 迁移虚拟人状态失败: {e}")
        
        logger.info(f"🎉 虚拟人状态迁移完成: {migrated_count} 条记录")
        return migrated_count
    
    def generate_user_profiles_from_conversations(self) -> int:
        """从对话记录生成用户画像"""
        logger.info("🔄 开始从对话记录生成用户画像...")
        
        generated_count = 0
        
        with self.pg_db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取所有用户的对话记录
            cursor.execute('''
                SELECT user_id, string_agg(message, ' ') as all_messages
                FROM conversations 
                WHERE sender = 'user'
                GROUP BY user_id
            ''')
            
            user_conversations = cursor.fetchall()
            
            for user_id, all_messages in user_conversations:
                try:
                    # 使用记忆服务提取画像
                    from services.memory_service import get_memory_service
                    memory_service = get_memory_service()
                    
                    # 分段处理长文本
                    message_chunks = [all_messages[i:i+500] for i in range(0, len(all_messages), 500)]
                    
                    for chunk in message_chunks[:3]:  # 限制处理前3个片段
                        memory_service.extract_memories_from_text(user_id, chunk)
                    
                    generated_count += 1
                    logger.info(f"🧠 为用户 {user_id} 生成画像")
                    
                except Exception as e:
                    logger.error(f"❌ 生成用户画像失败 {user_id}: {e}")
        
        logger.info(f"🎉 用户画像生成完成: {generated_count} 个用户")
        return generated_count
    
    def run_migration(self) -> bool:
        """执行完整迁移"""
        logger.info("🚀 开始数据迁移...")
        
        try:
            # 检查SQLite数据库
            if not self.check_sqlite_database():
                logger.warning("⚠️ 没有找到SQLite数据库，跳过迁移")
                return True
            
            # 执行迁移
            user_count = self.migrate_users()
            conv_count = self.migrate_conversations()
            aff_count = self.migrate_affection_levels()
            state_count = self.migrate_persona_states()
            profile_count = self.generate_user_profiles_from_conversations()
            
            # 显示迁移结果
            logger.info("\n" + "=" * 60)
            logger.info("🎉 数据迁移完成!")
            logger.info("=" * 60)
            logger.info(f"👥 用户: {user_count}")
            logger.info(f"💬 对话记录: {conv_count}")
            logger.info(f"💖 好感度记录: {aff_count}")
            logger.info(f"🤖 虚拟人状态: {state_count}")
            logger.info(f"🧠 用户画像: {profile_count}")
            logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            logger.error(f"💥 数据迁移失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False


def main():
    """主函数"""
    print("=" * 60)
    print("🔄 SQLite到PostgreSQL数据迁移工具")
    print("=" * 60)
    
    migrator = DataMigrator()
    success = migrator.run_migration()
    
    if success:
        print("\n✅ 迁移成功! 现在可以使用PostgreSQL记忆系统了。")
    else:
        print("\n❌ 迁移失败! 请检查日志信息。")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
