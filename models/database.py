"""
数据库基础设施模块
提供数据库连接、配置和初始化功能
"""

import os
import psycopg2
import psycopg2.extras
from typing import Optional
from contextlib import contextmanager

# PostgreSQL数据库连接配置
DATABASE_URL = os.getenv('MEMORY_DATABASE_URL', 'postgresql://memobase:memobase123@localhost:5433/memobase')

# 固定的项目ID，用于我们的虚拟人应用
PROJECT_ID = "virtual_companion_app"


@contextmanager
def get_db_connection():
    """获取数据库连接"""
    conn = psycopg2.connect(DATABASE_URL)
    try:
        yield conn
    finally:
        conn.close()


def _get_user_uuid(cursor, user_id: str) -> Optional[str]:
    """获取用户的UUID（内部辅助方法）"""
    cursor.execute('''
        SELECT id FROM users
        WHERE project_id = %s
        AND additional_fields->>'user_id' = %s
    ''', (PROJECT_ID, user_id))
    result = cursor.fetchone()
    return str(result[0]) if result else None


def _get_user_uuid_by_id(user_id: str) -> Optional[str]:
    """根据user_id获取UUID"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        return _get_user_uuid(cursor, user_id)


def init_database():
    """数据库初始化，确保项目和必要表存在"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 确保项目存在（如果projects表存在的话）
            try:
                cursor.execute('''
                    INSERT INTO projects (project_id, project_secret, status, id, created_at, updated_at)
                    VALUES (%s, %s, %s, gen_random_uuid(), NOW(), NOW())
                    ON CONFLICT (project_id) DO NOTHING
                ''', (PROJECT_ID, 'secret123', 'active'))
                conn.commit()
                print(f"✅ 项目 {PROJECT_ID} 已确保存在")
            except Exception as e:
                print(f"⚠️ 项目初始化跳过: {e}")

            # 创建必要的表
            _ensure_conversation_tables(cursor)
            _ensure_memory_tables(cursor)
            _ensure_user_tables(cursor)
            conn.commit()
            print("✅ 必要表已创建")

    except Exception as e:
        print(f"⚠️ 数据库初始化失败: {e}")


def _ensure_conversation_tables(cursor):
    """创建对话相关表"""
    # 对话表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS conversations (
            id SERIAL PRIMARY KEY,
            user_id UUID NOT NULL,
            project_id VARCHAR(64) NOT NULL DEFAULT %s,
            message TEXT NOT NULL,
            sender VARCHAR(20) NOT NULL,
            emotion_score FLOAT DEFAULT 0.0,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''', (PROJECT_ID,))
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id, project_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversations_timestamp ON conversations(timestamp)')

    # 好感度表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS affection_levels (
            id SERIAL PRIMARY KEY,
            user_id UUID NOT NULL,
            project_id VARCHAR(64) NOT NULL DEFAULT %s,
            level INTEGER NOT NULL,
            change_reason TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''', (PROJECT_ID,))
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_affection_user_id ON affection_levels(user_id, project_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_affection_timestamp ON affection_levels(timestamp)')


def _ensure_memory_tables(cursor):
    """创建记忆相关表"""
    # 人格状态表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS persona_states (
            id SERIAL PRIMARY KEY,
            project_id VARCHAR(64) NOT NULL DEFAULT %s,
            date DATE NOT NULL,
            current_activity TEXT,
            mood TEXT,
            work_content TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''', (PROJECT_ID,))
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_persona_states_date ON persona_states(date, project_id)')


def _ensure_user_tables(cursor):
    """创建用户相关表"""
    # 用户认证表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_auth (
            id SERIAL PRIMARY KEY,
            user_id UUID NOT NULL,
            project_id VARCHAR(64) NOT NULL DEFAULT %s,
            username VARCHAR(50) UNIQUE NOT NULL,
            password_hash TEXT,
            email VARCHAR(100),
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''', (PROJECT_ID,))
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_auth_username ON user_auth(username)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_auth_user_id ON user_auth(user_id, project_id)')

    # 用户会话表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_sessions (
            id SERIAL PRIMARY KEY,
            session_token VARCHAR(64) UNIQUE NOT NULL,
            user_id UUID NOT NULL,
            project_id VARCHAR(64) NOT NULL DEFAULT %s,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NOT NULL,
            is_active BOOLEAN DEFAULT TRUE
        )
    ''', (PROJECT_ID,))
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id, project_id)')
