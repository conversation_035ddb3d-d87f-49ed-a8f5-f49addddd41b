"""
用户相关模型
包含用户基础信息、认证和会话管理
"""

import json
import uuid
import secrets
import psycopg2.extras
from typing import Optional, Dict
from datetime import datetime, timedelta

from .database import get_db_connection, PROJECT_ID, _get_user_uuid_by_id


class User:
    """用户模型类"""

    def __init__(self, user_id: str = None, username: str = None,
                 name: str = None, additional_fields: Dict = None):
        self.user_id = user_id
        self.username = username
        self.name = name
        self.additional_fields = additional_fields or {}
        self.project_id = PROJECT_ID

    @classmethod
    def create(cls, user_id: str, name: str = None, additional_data: Dict = None):
        """创建新用户"""
        with get_db_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 尝试获取现有用户
            cursor.execute('''
                SELECT * FROM users
                WHERE project_id = %s
                AND additional_fields->>'user_id' = %s
            ''', (PROJECT_ID, user_id))
            user = cursor.fetchone()

            if user:
                return cls.from_dict(dict(user))

            # 创建新用户
            additional_fields = additional_data or {}
            additional_fields['user_id'] = user_id
            additional_fields['name'] = name or f'用户{user_id[:8]}'

            cursor.execute('''
                INSERT INTO users (id, project_id, additional_fields)
                VALUES (gen_random_uuid(), %s, %s)
                RETURNING *
            ''', (PROJECT_ID, json.dumps(additional_fields)))

            user_data = cursor.fetchone()
            conn.commit()
            return cls.from_dict(dict(user_data))

    @classmethod
    def from_dict(cls, data: Dict):
        """从字典创建用户对象"""
        additional_fields = data.get('additional_fields', {})
        return cls(
            user_id=additional_fields.get('user_id'),
            username=additional_fields.get('user_id'),
            name=additional_fields.get('name'),
            additional_fields=additional_fields
        )

    def get_uuid(self) -> Optional[str]:
        """获取用户的UUID"""
        return _get_user_uuid_by_id(self.user_id)

    @classmethod
    def get_or_create(cls, user_id: str, name: str = None) -> 'User':
        """获取或创建用户"""
        return cls.create(user_id, name)


class UserAuth:
    """用户认证模型"""

    def __init__(self, username: str, password_hash: str = None, email: str = None):
        self.username = username
        self.password_hash = password_hash
        self.email = email
        self.user_id = None
        self.status = 'active'
        self.created_at = None
        self.updated_at = None

    def save(self) -> str:
        """保存用户认证信息，返回用户ID"""
        if not self.user_id:
            self.user_id = str(uuid.uuid4())

        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO user_auth (user_id, project_id, username, password_hash, email, status)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (username) DO UPDATE SET
                    password_hash = EXCLUDED.password_hash,
                    email = EXCLUDED.email,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING user_id
            ''', (self.user_id, PROJECT_ID, self.username, self.password_hash, self.email, self.status))

            result = cursor.fetchone()
            self.user_id = str(result[0])
            conn.commit()

        return self.user_id

    @classmethod
    def find_by_username(cls, username: str) -> Optional['UserAuth']:
        """根据用户名查找用户"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT user_id, username, password_hash, email, status, created_at, updated_at
                FROM user_auth
                WHERE username = %s AND project_id = %s
            ''', (username, PROJECT_ID))

            result = cursor.fetchone()
            if not result:
                return None

            user_id, username, password_hash, email, status, created_at, updated_at = result

            user_auth = cls(username, password_hash, email)
            user_auth.user_id = str(user_id)
            user_auth.status = status
            user_auth.created_at = created_at
            user_auth.updated_at = updated_at

            return user_auth

    @classmethod
    def username_exists(cls, username: str) -> bool:
        """检查用户名是否已存在"""
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT 1 FROM user_auth
                WHERE username = %s AND project_id = %s
            ''', (username, PROJECT_ID))
            return cursor.fetchone() is not None

    @classmethod
    def ensure_tables(cls):
        """确保用户认证表存在"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 创建用户认证表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_auth (
                    id SERIAL PRIMARY KEY,
                    user_id UUID NOT NULL,
                    project_id VARCHAR(64) NOT NULL DEFAULT %s,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password_hash TEXT,
                    email VARCHAR(100),
                    status VARCHAR(20) DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''', (PROJECT_ID,))

            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_auth_username ON user_auth(username)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_auth_user_id ON user_auth(user_id, project_id)')

            conn.commit()


class UserSession:
    """用户会话模型"""

    def __init__(self, user_id: str, session_token: str = None, expires_at: datetime = None):
        self.user_id = user_id
        self.session_token = session_token
        self.expires_at = expires_at
        self.is_active = True
        self.created_at = None

    def save(self) -> str:
        """保存会话信息，返回会话令牌"""
        if not self.session_token:
            self.session_token = secrets.token_urlsafe(32)

        if not self.expires_at:
            self.expires_at = datetime.now() + timedelta(days=7)

        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO user_sessions (session_token, user_id, project_id, expires_at, is_active)
                VALUES (%s, %s, %s, %s, %s)
            ''', (self.session_token, self.user_id, PROJECT_ID, self.expires_at, self.is_active))

            conn.commit()

        return self.session_token

    @classmethod
    def find_by_token(cls, session_token: str) -> Optional['UserSession']:
        """根据会话令牌查找会话"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT s.user_id, s.session_token, s.expires_at, s.is_active, s.created_at,
                       u.username, u.email, u.status
                FROM user_sessions s
                JOIN user_auth u ON s.user_id = u.user_id AND s.project_id = u.project_id
                WHERE s.session_token = %s AND s.project_id = %s
            ''', (session_token, PROJECT_ID))

            result = cursor.fetchone()
            if not result:
                return None

            user_id, session_token, expires_at, is_active, created_at, username, email, status = result

            session = cls(str(user_id), session_token, expires_at)
            session.is_active = is_active
            session.created_at = created_at
            session.user_info = {
                'username': username,
                'email': email,
                'status': status
            }

            return session

    def is_valid(self) -> bool:
        """检查会话是否有效"""
        if not self.is_active:
            return False

        if datetime.now() > self.expires_at:
            return False

        if hasattr(self, 'user_info') and self.user_info.get('status') != 'active':
            return False

        return True

    def deactivate(self) -> bool:
        """停用会话"""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE user_sessions
                    SET is_active = FALSE
                    WHERE session_token = %s AND project_id = %s
                ''', (self.session_token, PROJECT_ID))

                conn.commit()
                result = cursor.rowcount > 0

                if result:
                    self.is_active = False

                return result
        except Exception:
            return False

    @classmethod
    def ensure_tables(cls):
        """确保用户会话表存在"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 创建用户会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id SERIAL PRIMARY KEY,
                    session_token VARCHAR(64) UNIQUE NOT NULL,
                    user_id UUID NOT NULL,
                    project_id VARCHAR(64) NOT NULL DEFAULT %s,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE
                )
            ''', (PROJECT_ID,))

            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id, project_id)')

            conn.commit()
