"""
Models包初始化文件
导出所有模型类和数据库初始化函数
"""

# 数据库基础设施
from .database import (
    get_db_connection,
    PROJECT_ID,
    init_database
)

# 用户相关模型
from .user import (
    User,
    UserAuth,
    UserSession
)

# 对话相关模型
from .conversation import (
    Conversation,
    AffectionLevel
)

# 记忆相关模型
from .memory import (
    UserEvent,
    PersonaState
)

# 初始化数据库
init_database()

__all__ = [
    # 数据库
    'get_db_connection',
    'PROJECT_ID',
    'init_database',
    
    # 用户
    'User',
    'UserAuth', 
    'UserSession',
    
    # 对话
    'Conversation',
    'AffectionLevel',
    
    # 记忆
    'UserEvent',
    'PersonaState'
]
