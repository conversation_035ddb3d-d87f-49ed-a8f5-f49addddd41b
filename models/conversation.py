"""
对话相关模型
包含对话记录和好感度管理
"""

import psycopg2.extras
from typing import List, Dict
from datetime import datetime

from .database import get_db_connection, PROJECT_ID, _get_user_uuid_by_id
from .user import User


class Conversation:
    """对话模型类"""

    def __init__(self, user_id: str = None, message: str = None,
                 sender: str = None, emotion_score: float = 0.0, timestamp: datetime = None):
        self.user_id = user_id
        self.message = message
        self.sender = sender
        self.emotion_score = emotion_score
        self.timestamp = timestamp
        self.project_id = PROJECT_ID

    def save(self):
        """保存对话记录"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(self.user_id)
            if not user_uuid:
                # 如果用户不存在，先创建用户
                User.create(self.user_id)
                user_uuid = _get_user_uuid_by_id(self.user_id)

            cursor.execute('''
                INSERT INTO conversations (user_id, project_id, message, sender, emotion_score)
                VALUES (%s, %s, %s, %s, %s)
            ''', (user_uuid, self.project_id, self.message, self.sender, self.emotion_score))

            conn.commit()

    @classmethod
    def get_recent(cls, user_id: str, limit: int = 10) -> List[Dict]:
        """获取最近对话"""
        with get_db_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(user_id)
            if not user_uuid:
                return []

            cursor.execute('''
                SELECT * FROM conversations
                WHERE user_id = %s AND project_id = %s
                ORDER BY timestamp DESC LIMIT %s
            ''', (user_uuid, PROJECT_ID, limit))

            return [dict(row) for row in cursor.fetchall()]


class AffectionLevel:
    """好感度模型类"""

    def __init__(self, user_id: str = None, level: int = 10,
                 change_reason: str = None):
        self.user_id = user_id
        self.level = level
        self.change_reason = change_reason
        self.project_id = PROJECT_ID

    def update(self, change: int, reason: str) -> int:
        """更新好感度"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(self.user_id)
            if not user_uuid:
                # 如果用户不存在，先创建用户
                User.create(self.user_id)
                user_uuid = _get_user_uuid_by_id(self.user_id)

            # 获取当前好感度
            cursor.execute('''
                SELECT level FROM affection_levels
                WHERE user_id = %s AND project_id = %s
                ORDER BY timestamp DESC LIMIT 1
            ''', (user_uuid, self.project_id))

            result = cursor.fetchone()
            current_level = result[0] if result else 10  # 默认初始好感度

            # 计算新好感度
            new_level = max(0, min(100, current_level + change))

            # 记录好感度变化
            cursor.execute('''
                INSERT INTO affection_levels (user_id, project_id, level, change_reason)
                VALUES (%s, %s, %s, %s)
            ''', (user_uuid, self.project_id, new_level, reason))

            conn.commit()
            self.level = new_level
            return new_level

    @classmethod
    def get_current(cls, user_id: str) -> int:
        """获取当前好感度"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(user_id)
            if not user_uuid:
                return 10  # 默认初始好感度

            cursor.execute('''
                SELECT level FROM affection_levels
                WHERE user_id = %s AND project_id = %s
                ORDER BY timestamp DESC LIMIT 1
            ''', (user_uuid, PROJECT_ID))

            result = cursor.fetchone()
            return result[0] if result else 10  # 默认初始好感度
