"""
记忆相关模型
包含用户事件和人格状态管理
"""

import json
import psycopg2.extras
from typing import Dict, List, Optional
from datetime import datetime, date

from .database import get_db_connection, PROJECT_ID, _get_user_uuid_by_id
from .user import User


class UserEvent:
    """用户事件模型"""

    def __init__(self, user_id: str, event_data: Dict, embedding: List[float] = None):
        self.user_id = user_id
        self.event_data = event_data
        self.embedding = embedding
        self.id = None
        self.created_at = None
        self.updated_at = None

    def save(self):
        """保存用户事件"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(self.user_id)
            if not user_uuid:
                # 如果用户不存在，先创建用户
                User.create(self.user_id)
                user_uuid = _get_user_uuid_by_id(self.user_id)

            # 保存事件数据
            cursor.execute('''
                INSERT INTO user_events (user_id, project_id, event_data, embedding)
                VALUES (%s, %s, %s, %s)
                RETURNING id
            ''', (user_uuid, PROJECT_ID, json.dumps(self.event_data), self.embedding))

            self.id = cursor.fetchone()[0]
            conn.commit()

    @classmethod
    def get_by_user(cls, user_id: str, limit: int = 50) -> List['UserEvent']:
        """获取用户的事件记录"""
        with get_db_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(user_id)
            if not user_uuid:
                return []

            cursor.execute('''
                SELECT * FROM user_events
                WHERE user_id = %s AND project_id = %s
                ORDER BY created_at DESC LIMIT %s
            ''', (user_uuid, PROJECT_ID, limit))

            events = []
            for row in cursor.fetchall():
                event = cls(user_id, json.loads(row['event_data']), row['embedding'])
                event.id = row['id']
                event.created_at = row['created_at']
                event.updated_at = row['updated_at']
                events.append(event)

            return events

    @classmethod
    def search_similar(cls, user_id: str, query_embedding: List[float], 
                      limit: int = 10, threshold: float = 0.7) -> List['UserEvent']:
        """搜索相似的事件"""
        with get_db_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 获取用户的UUID
            user_uuid = _get_user_uuid_by_id(user_id)
            if not user_uuid:
                return []

            cursor.execute('''
                SELECT *, (embedding <=> %s::vector) as distance
                FROM user_events
                WHERE user_id = %s AND project_id = %s
                AND embedding IS NOT NULL
                ORDER BY distance
                LIMIT %s
            ''', (query_embedding, user_uuid, PROJECT_ID, limit))

            events = []
            for row in cursor.fetchall():
                if row['distance'] <= (1 - threshold):  # 转换相似度阈值
                    event = cls(user_id, json.loads(row['event_data']), row['embedding'])
                    event.id = row['id']
                    event.created_at = row['created_at']
                    event.updated_at = row['updated_at']
                    events.append(event)

            return events


class PersonaState:
    """人格状态模型"""

    def __init__(self, date: date = None, current_activity: str = None,
                 mood: str = None, work_content: str = None):
        self.date = date or datetime.now().date()
        self.current_activity = current_activity
        self.mood = mood
        self.work_content = work_content
        self.project_id = PROJECT_ID

    def save(self):
        """保存人格状态"""
        with get_db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO persona_states (project_id, date, current_activity, mood, work_content)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (project_id, date) DO UPDATE SET
                    current_activity = EXCLUDED.current_activity,
                    mood = EXCLUDED.mood,
                    work_content = EXCLUDED.work_content
            ''', (self.project_id, self.date, self.current_activity, self.mood, self.work_content))

            conn.commit()

    @classmethod
    def get_current(cls) -> Optional['PersonaState']:
        """获取当前人格状态"""
        today = datetime.now().date()
        return cls.get_by_date(today)

    @classmethod
    def get_by_date(cls, target_date: date) -> Optional['PersonaState']:
        """根据日期获取人格状态"""
        with get_db_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            cursor.execute('''
                SELECT * FROM persona_states
                WHERE project_id = %s AND date = %s
            ''', (PROJECT_ID, target_date))

            result = cursor.fetchone()
            if not result:
                return None

            return cls(
                date=result['date'],
                current_activity=result['current_activity'],
                mood=result['mood'],
                work_content=result['work_content']
            )

    @classmethod
    def get_recent(cls, days: int = 7) -> List['PersonaState']:
        """获取最近几天的人格状态"""
        with get_db_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            cursor.execute('''
                SELECT * FROM persona_states
                WHERE project_id = %s
                ORDER BY date DESC LIMIT %s
            ''', (PROJECT_ID, days))

            states = []
            for row in cursor.fetchall():
                states.append(cls(
                    date=row['date'],
                    current_activity=row['current_activity'],
                    mood=row['mood'],
                    work_content=row['work_content']
                ))

            return states
