#!/usr/bin/env python3
"""
测试虚拟人ID管理系统
验证ID的唯一性、一致性和持久化
"""

import sys
import os
import json
import time

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.persona_id_manager import get_persona_id_manager, PersonaIdManager


def test_persona_id_consistency():
    """测试虚拟人ID的一致性"""
    print("🧪 测试虚拟人ID一致性...")
    
    # 获取多个管理器实例
    manager1 = get_persona_id_manager()
    manager2 = get_persona_id_manager()
    
    # 验证单例模式
    assert manager1 is manager2, "❌ 虚拟人ID管理器应该是单例"
    print("✅ 单例模式验证通过")
    
    # 获取ID
    id1 = manager1.get_persona_id()
    id2 = manager2.get_persona_id()
    
    # 验证ID一致性
    assert id1 == id2, f"❌ 虚拟人ID应该一致: {id1} != {id2}"
    print(f"✅ ID一致性验证通过: {id1}")
    
    return id1


def test_persona_id_persistence():
    """测试虚拟人ID的持久化"""
    print("\n🧪 测试虚拟人ID持久化...")
    
    # 获取当前ID
    manager = get_persona_id_manager()
    original_id = manager.get_persona_id()
    print(f"📋 原始ID: {original_id}")
    
    # 检查文件是否存在
    id_file_path = os.path.join(
        os.path.dirname(__file__), 
        'backend', 
        'database', 
        'persona_id.json'
    )
    
    assert os.path.exists(id_file_path), f"❌ ID文件不存在: {id_file_path}"
    print("✅ ID文件存在")
    
    # 读取文件内容
    with open(id_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    assert data['persona_id'] == original_id, "❌ 文件中的ID与内存中的ID不一致"
    print("✅ 文件持久化验证通过")
    
    # 验证文件格式
    required_fields = ['persona_id', 'created_at', 'persona_name', 'persona_nickname', 'version']
    for field in required_fields:
        assert field in data, f"❌ 缺少必需字段: {field}"
    print("✅ 文件格式验证通过")
    
    return data


def test_persona_id_format():
    """测试虚拟人ID格式"""
    print("\n🧪 测试虚拟人ID格式...")
    
    manager = get_persona_id_manager()
    persona_id = manager.get_persona_id()
    
    # 验证ID格式
    assert persona_id.startswith('persona_shenmuxi_'), f"❌ ID应该以'persona_shenmuxi_'开头: {persona_id}"
    print("✅ ID前缀验证通过")
    
    # 验证ID长度（应该足够长以确保唯一性）
    assert len(persona_id) > 20, f"❌ ID长度应该足够长: {len(persona_id)}"
    print("✅ ID长度验证通过")
    
    # 验证ID只包含允许的字符
    allowed_chars = set('abcdefghijklmnopqrstuvwxyz0123456789_')
    id_chars = set(persona_id.lower())
    assert id_chars.issubset(allowed_chars), f"❌ ID包含不允许的字符: {id_chars - allowed_chars}"
    print("✅ ID字符验证通过")
    
    return persona_id


def test_persona_id_info():
    """测试虚拟人ID信息获取"""
    print("\n🧪 测试虚拟人ID信息获取...")
    
    manager = get_persona_id_manager()
    id_info = manager.get_id_info()
    
    # 验证信息完整性
    assert isinstance(id_info, dict), "❌ ID信息应该是字典类型"
    assert 'persona_id' in id_info, "❌ 缺少persona_id字段"
    assert 'persona_name' in id_info, "❌ 缺少persona_name字段"
    assert 'persona_nickname' in id_info, "❌ 缺少persona_nickname字段"
    
    print("✅ ID信息获取验证通过")
    print(f"📋 虚拟人姓名: {id_info.get('persona_name')}")
    print(f"📋 虚拟人昵称: {id_info.get('persona_nickname')}")
    print(f"📋 创建时间: {id_info.get('created_at')}")
    
    return id_info


def test_new_manager_instance():
    """测试新管理器实例的ID一致性"""
    print("\n🧪 测试新管理器实例...")
    
    # 获取当前ID
    current_id = get_persona_id_manager().get_persona_id()
    
    # 创建新的管理器实例（绕过单例）
    new_manager = PersonaIdManager()
    new_id = new_manager.get_persona_id()
    
    # 验证ID一致性
    assert current_id == new_id, f"❌ 新管理器实例的ID应该与现有ID一致: {current_id} != {new_id}"
    print("✅ 新管理器实例ID一致性验证通过")
    
    return new_id


def main():
    """主测试函数"""
    print("🤖 虚拟人ID管理系统测试")
    print("=" * 50)
    
    try:
        # 运行所有测试
        test_persona_id_consistency()
        test_persona_id_persistence()
        test_persona_id_format()
        test_persona_id_info()
        test_new_manager_instance()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！虚拟人ID管理系统工作正常")
        
        # 显示最终的虚拟人ID
        final_id = get_persona_id_manager().get_persona_id()
        print(f"🆔 当前虚拟人ID: {final_id}")
        
    except AssertionError as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试出错: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
