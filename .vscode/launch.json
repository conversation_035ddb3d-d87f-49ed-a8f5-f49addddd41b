{"version": "0.2.0", "configurations": [{"name": "Debug Backend App", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/backend/app.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/backend", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}/backend"}, "args": [], "justMyCode": false, "stopOnEntry": false, "jinja": true, "autoStartBrowser": false}, {"name": "Debug with start.py", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/start.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1"}, "args": [], "justMyCode": false, "stopOnEntry": false, "jinja": true, "autoStartBrowser": false}]}