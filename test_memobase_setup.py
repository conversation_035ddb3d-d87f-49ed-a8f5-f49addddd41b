#!/usr/bin/env python3
"""
测试Memobase设置
验证Memobase SDK和配置是否正确
"""

import sys
import os

# 添加backend目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_memobase_sdk():
    """测试Memobase SDK"""
    print("🔍 测试Memobase SDK...")
    
    try:
        import memobase
        print(f"✅ Memobase SDK导入成功，版本: {getattr(memobase, '__version__', '未知')}")
        
        # 测试主要类
        from memobase import MemoBaseClient, ChatBlob
        print("✅ Memobase主要类导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ Memobase SDK导入失败: {e}")
        print("💡 请运行: pip install memobase")
        return False

def test_config():
    """测试配置"""
    print("\n⚙️ 测试Memobase配置...")
    
    try:
        from config import Config
        
        # 检查Memobase配置
        memobase_config = Config.MEMOBASE_CONFIG
        print("✅ Memobase配置加载成功")
        print(f"   项目URL: {memobase_config['project_url']}")
        print(f"   项目ID: {memobase_config['project_id']}")
        print(f"   API密钥: {memobase_config['api_key'][:10]}...")
        print(f"   最大Token数: {memobase_config['max_token_size']}")
        print(f"   自动刷新: {memobase_config['auto_flush']}")
        print(f"   优先话题: {memobase_config['prefer_topics']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_client_creation():
    """测试客户端创建（不连接服务器）"""
    print("\n🔧 测试Memobase客户端创建...")
    
    try:
        from clients.memobase_client import MemobaseClient
        
        # 尝试创建客户端（可能会因为服务器未运行而失败，但这是正常的）
        try:
            client = MemobaseClient()
            print("✅ Memobase客户端创建成功")
            return True
        except Exception as e:
            if "连接" in str(e) or "connection" in str(e).lower():
                print("⚠️ Memobase客户端创建成功，但无法连接服务器（这是正常的，服务器未启动）")
                print(f"   错误信息: {e}")
                return True
            else:
                print(f"❌ Memobase客户端创建失败: {e}")
                return False
        
    except Exception as e:
        print(f"❌ 客户端创建测试失败: {e}")
        return False

def test_memory_managers():
    """测试记忆管理器"""
    print("\n🧠 测试记忆管理器...")
    
    try:
        from services.memobase_memory_manager import MemobaseMemoryManager
        from services.memobase_persona_memory_manager import MemobasePersonaMemoryManager
        
        print("✅ 记忆管理器类导入成功")
        
        # 尝试创建管理器（可能会因为服务器未运行而失败）
        try:
            memory_manager = MemobaseMemoryManager()
            print("✅ 用户记忆管理器创建成功")
        except Exception as e:
            if "连接" in str(e) or "connection" in str(e).lower():
                print("⚠️ 用户记忆管理器创建失败（服务器未启动，这是正常的）")
            else:
                print(f"❌ 用户记忆管理器创建失败: {e}")
        
        try:
            persona_manager = MemobasePersonaMemoryManager()
            print("✅ 虚拟人记忆管理器创建成功")
        except Exception as e:
            if "连接" in str(e) or "connection" in str(e).lower():
                print("⚠️ 虚拟人记忆管理器创建失败（服务器未启动，这是正常的）")
            else:
                print(f"❌ 虚拟人记忆管理器创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 记忆管理器测试失败: {e}")
        return False

def test_conversation_engine():
    """测试对话引擎（不启动）"""
    print("\n💬 测试对话引擎导入...")
    
    try:
        from services.conversation_engine import ConversationEngine
        print("✅ 对话引擎类导入成功")
        
        # 不实际创建实例，因为需要LLM配置
        print("⚠️ 对话引擎需要火山引擎API配置才能启动")
        
        return True
        
    except Exception as e:
        print(f"❌ 对话引擎导入失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n📝 后续步骤:")
    print("1. 启动Memobase服务:")
    print("   cd memobase/src/server")
    print("   # 确保Docker已启动")
    print("   docker-compose up -d")
    print("")
    print("2. 配置火山引擎API:")
    print("   cp backend/.env.example backend/.env")
    print("   # 编辑 .env 文件，填入火山引擎API配置")
    print("")
    print("3. 启动项目:")
    print("   python start.py")
    print("")
    print("4. 测试对话功能:")
    print("   访问 http://localhost:8080")

def main():
    """主测试函数"""
    print("🚀 Memobase设置验证测试")
    print("=" * 50)
    
    tests = [
        ("Memobase SDK", test_memobase_sdk),
        ("配置测试", test_config),
        ("客户端创建", test_client_creation),
        ("记忆管理器", test_memory_managers),
        ("对话引擎导入", test_conversation_engine),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed >= 3:  # 至少SDK、配置、导入测试通过
        print("🎉 Memobase基础设置完成！")
        show_next_steps()
    else:
        print("⚠️ 基础设置存在问题，请检查配置")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
